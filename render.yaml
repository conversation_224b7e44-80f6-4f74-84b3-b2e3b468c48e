services:
  - type: web
    name: bakedBot
    env: docker
    repo: https://github.com/Dashon/smokey-ai.git
    dockerfilePath: ./docker/Dockerfile.render
    dockerContext: .
    buildCommand: docker build 
      --build-arg REACT_APP_FIREBASE_API_KEY=${REACT_APP_FIREBASE_API_KEY}
      --build-arg REACT_APP_FIREBASE_AUTH_DOMAIN=${REACT_APP_FIREBASE_AUTH_DOMAIN}
      --build-arg REACT_APP_FIREBASE_PROJECT_ID=${REACT_APP_FIREBASE_PROJECT_ID}
      --build-arg REACT_APP_FIREBASE_STORAGE_BUCKET=${REACT_APP_FIREBASE_STORAGE_BUCKET}
      --build-arg REACT_APP_FIREBASE_MESSAGING_SENDER_ID=${REACT_APP_FIREBASE_MESSAGING_SENDER_ID}
      --build-arg REACT_APP_FIREBASE_APP_ID=${REACT_APP_FIREBASE_APP_ID}
      --build-arg REACT_APP_FIREBASE_MEASUREMENT_ID=${REACT_APP_FIREBASE_MEASUREMENT_ID}
      --build-arg REACT_APP_BASE_URL=${REACT_APP_BASE_URL}
      --build-arg REACT_APP_STRIPE_PUBLISHABLE_KEY=${REACT_APP_STRIPE_PUBLISHABLE_KEY}
      --build-arg REACT_APP_STRIPE_PRICING_TABLE_ID=${REACT_APP_STRIPE_PRICING_TABLE_ID}
      -f ./docker/Dockerfile.render .
    autoDeploy: false
    healthCheckPath: /api/health
    envVars:
      - key: NODE_ENV
        value: production
      - key: BASE_URL
        sync: false
      - key: MONO
        value: "true"
      - key: PORT
        value: 80
      - key: APP_SECRET
        generateValue: true
      - key: DB_CLIENT
        value: mysql2
      - key: DB_HOST
        fromService:
          type: pserv
          name: mysql
          property: host
      - key: DB_USERNAME
        fromService:
          type: pserv
          name: mysql
          envVarKey: MYSQL_USER
      - key: DB_PASSWORD
        fromService:
          type: pserv
          name: mysql
          envVarKey: MYSQL_PASSWORD
      - key: DB_PORT
        value: 3306
      - key: DB_DATABASE
        fromService:
          type: pserv
          name: mysql
          envVarKey: MYSQL_DATABASE
      - key: STORAGE_DRIVER
        value: local
      - key: QUEUE_DRIVER
        value: redis
      - key: REDIS_HOST
        fromService:
          type: redis
          name: lightning
          property: host
      - key: REDIS_PORT
        fromService:
          type: redis
          name: lightning
          property: port
      - key: AUTH_DRIVER
        value: basic
      - key: AUTH_BASIC_EMAIL
        sync: false
      - key: AUTH_BASIC_PASSWORD
        sync: false
      - key: LOG_COMPILED_MESSAGE
        value: "true"
      - key: STORAGE_BASE_URL
        value: https://bakedBot-api.onrender.com/uploads
      - key: STORAGE_S3_BUCKET
        sync: false
      - key: STORAGE_S3_ENDPOINT
        sync: false
      - key: STORAGE_S3_FORCE_PATH_STYLE
        sync: false
      - key: AWS_S3_BUCKET
        sync: false
      - key: AWS_ACCESS_KEY_ID
        sync: false
      - key: AWS_SECRET_ACCESS_KEY
        sync: false
      - key: AWS_REGION
        sync: false
      - key: AUTH_BASIC_NAME
        value: "Login"
      - key: AUTH_FIREBASE_CREDENTIALS
        sync: false
      - key: AUTH_FIREBASE_NAME
        value: "Firebase"
      - key: ERROR_DRIVER
        sync: false
      - key: ERROR_BUGSNAG_API_KEY
        sync: false
      - key: ERROR_SENTRY_DSN
        sync: false
      - key: TRACKING_LINK_WRAP
        sync: false
      - key: TRACKING_DEEPLINK_MIRROR_URL
        sync: false
      - key: FIREBASE_API_KEY
        sync: false
      - key: FIREBASE_AUTH_DOMAIN
        sync: false
      - key: FIREBASE_PROJECT_ID
        sync: false
      - key: FIREBASE_STORAGE_BUCKET
        sync: false
      - key: FIREBASE_MESSAGING_SENDER_ID
        sync: false
      - key: FIREBASE_APP_ID
        sync: false
      - key: FIREBASE_MEASUREMENT_ID
        sync: false
      - key: REACT_APP_FIREBASE_API_KEY
        sync: false
      - key: REACT_APP_FIREBASE_AUTH_DOMAIN
        sync: false
      - key: REACT_APP_FIREBASE_PROJECT_ID
        sync: false
      - key: REACT_APP_FIREBASE_STORAGE_BUCKET
        sync: false
      - key: REACT_APP_FIREBASE_MESSAGING_SENDER_ID
        sync: false
      - key: REACT_APP_FIREBASE_APP_ID
        sync: false
      - key: REACT_APP_FIREBASE_MEASUREMENT_ID
        sync: false
      - key: REACT_APP_BASE_URL
        value: https://bakedbot.onrender.com/api/v1
      - key: TWILIO_AUTH_TOKEN
        sync: false
      - key: TWILIO_ACCOUNT_SID
        sync: false
      - key: TWILIO_PHONE_NUMBER
        sync: false
      - key: SENDGRID_API_KEY
        sync: false
      - key: PINECONE_API_KEY
        sync: false
      - key: OPENAI_API_KEY
        sync: false
      - key: SUPABASE_URL
        sync: false
      - key: SUPABASE_KEY
        sync: false
      - key: SUPABASE_BUCKET
        sync: false 
      - key: SUPABASE_TOKEN
        sync: false
      - key: SUPABASE_WORKSPACE_ID
        sync: false
      - key: SUPABASE_API_TOKEN
        sync: false
      - key: SUPABASE_SQL_WAREHOUSE_ID
        sync: false
      - key: APP_CHECK_SECRET
        sync: false
      - key: OAUTH_LINKEDIN_REDIRECT_URI
        sync: false
      - key: OAUTH_LINKEDIN_CLIENT_ID
        sync: false
      - key: OAUTH_LINKEDIN_CLIENT_SECRET
        sync: false
      - key: REACT_APP_API_BASE_URL
        value: /api
      - key: REACT_APP_PROXY_URL
        value: http://api:3001
      - key: REACT_APP_AUTH_CALLBACK_URL
        sync: false
      - key: REACT_APP_FACEBOOK_APP_ID
        sync: false
      - key: REACT_APP_LINKEDIN_CLIENT_ID
        sync: false
      - key: REACT_APP_STRIPE_PUBLISHABLE_KEY
        sync: false
      - key: REACT_APP_STRIPE_PRICING_TABLE_ID
        sync: false
      - key: STRIPE_SECRET_KEY
        sync: false
      - key: STRIPE_ADD_ON_PRICE_ID
        sync: false
      - key: STRIPE_WEBHOOK_SECRET
        sync: false
      - key: STRIPE_ENDPOINT_SECRET
        sync: false
      - key: RAG_PYTHON_URL
        value: http://rag-python:8000

  # - type: worker
  #   name: rag-python
  #   env: docker
  #   repo: https://github.com/Dashon/smokey-ai.git
  #   dockerfilePath: ./docker/rag-python.Dockerfile
  #   dockerContext: .
  #   autoDeploy: false
  #   envVars:
  #     - key: OPENAI_API_KEY
  #       sync: false
  #     - key: PINECONE_API_KEY
  #       sync: false
  #     - key: MYSQL_HOST
  #       fromService:
  #         type: pserv
  #         name: mysql
  #         property: host
  #     - key: MYSQL_DATABASE
  #       fromService:
  #         type: pserv
  #         name: mysql
  #         envVarKey: MYSQL_DATABASE
  #     - key: MYSQL_USER
  #       fromService:
  #         type: pserv
  #         name: mysql
  #         envVarKey: MYSQL_USER
  #     - key: MYSQL_PASSWORD
  #       fromService:
  #         type: pserv
  #         name: mysql
  #         envVarKey: MYSQL_PASSWORD
  #     - key: SUPABASE_URL
  #       sync: false
  #     - key: SUPABASE_SERVICE_ROLE_KEY
  #       sync: false
  #     - key: LOG_LEVEL
  #       value: info

  - type: redis
    name: lightning
    ipAllowList: []
    maxmemoryPolicy: noeviction # optional (defaults to allkeys-lru)
  - type: pserv
    name: mysql
    plan: standard
    env: docker
    repo: https://github.com/render-examples/mysql
    autoDeploy: false
    disk:
      name: mysql
      mountPath: /var/lib/mysql
      sizeGB: 10
    envVars:
    - key: MYSQL_ROOT_PASSWORD
      generateValue: true
    - key: MYSQL_DATABASE
      value: bakedBot
    - key: MYSQL_USER
      value: bakedBot
    - key: MYSQL_PASSWORD
      generateValue: true
