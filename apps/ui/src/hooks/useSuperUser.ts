import { useState, useEffect } from 'react';
import { client } from '../api';
import useAuth from './useAuth';

export const useSuperUser = () => {
  const [isSuperUser, setIsSuperUser] = useState(false);
  const [loading, setLoading] = useState(true);
  const { user } = useAuth();

  useEffect(() => {
    const checkSuperUserStatus = async () => {
      if (!user) {
        setIsSuperUser(false);
        setLoading(false);
        return;
      }

      try {
        // Try to access the Mixpanel admin status endpoint
        // If it succeeds, the user is a super user
        await client.get('/admin/mixpanel-admin/status');
        setIsSuperUser(true);
      } catch (error: any) {
        // If it fails with 403, user is not a super user
        // If it fails with other errors, we assume not a super user for safety
        setIsSuperUser(false);
      } finally {
        setLoading(false);
      }
    };

    checkSuperUserStatus();
  }, [user]);

  return { isSuperUser, loading };
};
