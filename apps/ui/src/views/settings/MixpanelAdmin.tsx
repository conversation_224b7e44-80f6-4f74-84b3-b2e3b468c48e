import React, { useState, useEffect } from 'react';
import Card from '../../ui/Card';
import Button from '../../ui/Button';
import Heading from '../../ui/Heading';
import Alert from '../../ui/Alert';
import Spinner from '../../ui/Spinner';

import { client } from '../../api';

interface MixpanelStatus {
  initialized: boolean;
  timestamp: string;
  admin: string;
}

export const MixpanelAdmin: React.FC = () => {
  const [status, setStatus] = useState<MixpanelStatus | null>(null);
  const [loading, setLoading] = useState(false);
  const [error, setError] = useState<string | null>(null);
  const [success, setSuccess] = useState<string | null>(null);

  // Form states
  const [trackEventForm, setTrackEventForm] = useState({
    userId: '',
    eventName: '',
    properties: '{}'
  });

  const [setPropertiesForm, setSetPropertiesForm] = useState({
    userId: '',
    properties: '{}'
  });

  const [incrementForm, setIncrementForm] = useState({
    userId: '',
    property: '',
    value: 1
  });

  // Check if user is super user (this would be determined by the backend)
  const [isSuperUser, setIsSuperUser] = useState(false);

  useEffect(() => {
    checkStatus();
    // In a real implementation, you'd check if the user is a super user
    // For now, we'll assume they are if they can access this component
    setIsSuperUser(true);
  }, []);

  const checkStatus = async () => {
    try {
      setLoading(true);
      const response = await client.get('/admin/mixpanel-admin/status');
      setStatus(response.data);
    } catch (err: any) {
      if (err.response?.status === 403) {
        setError('Super user access required');
        setIsSuperUser(false);
      } else {
        setError('Failed to check Mixpanel status');
      }
    } finally {
      setLoading(false);
    }
  };

  const handleTrackEvent = async (e: React.FormEvent) => {
    e.preventDefault();
    try {
      setLoading(true);
      setError(null);
      setSuccess(null);

      const properties = trackEventForm.properties ? JSON.parse(trackEventForm.properties) : {};

      const response = await client.post('/admin/mixpanel-admin/track', {
        userId: trackEventForm.userId,
        eventName: trackEventForm.eventName,
        properties
      });

      setSuccess(response.data.message);
      setTrackEventForm({ userId: '', eventName: '', properties: '{}' });
    } catch (err: any) {
      setError(err.response?.data?.error || 'Failed to track event');
    } finally {
      setLoading(false);
    }
  };

  const handleSetProperties = async (e: React.FormEvent) => {
    e.preventDefault();
    try {
      setLoading(true);
      setError(null);
      setSuccess(null);
      
      const properties = JSON.parse(setPropertiesForm.properties);
      
      const response = await client.post('/admin/mixpanel-admin/properties', {
        userId: setPropertiesForm.userId,
        properties
      });
      
      setSuccess(response.data.message);
      setSetPropertiesForm({ userId: '', properties: '{}' });
    } catch (err: any) {
      setError(err.response?.data?.error || 'Failed to set properties');
    } finally {
      setLoading(false);
    }
  };

  const handleIncrement = async (e: React.FormEvent) => {
    e.preventDefault();
    try {
      setLoading(true);
      setError(null);
      setSuccess(null);
      
      const response = await client.post('/admin/mixpanel-admin/increment', {
        userId: incrementForm.userId,
        property: incrementForm.property,
        value: incrementForm.value
      });
      
      setSuccess(response.data.message);
      setIncrementForm({ userId: '', property: '', value: 1 });
    } catch (err: any) {
      setError(err.response?.data?.error || 'Failed to increment property');
    } finally {
      setLoading(false);
    }
  };

  if (!isSuperUser) {
    return (
      <Card>
        <div className="space-y-4 p-6">
          <Heading title="Mixpanel Admin" size="h2" />
          <Alert variant="error" title="Access Denied">
            Super user access required. This feature is only available to configured super users.
          </Alert>
        </div>
      </Card>
    );
  }

  return (
    <div className="space-y-6">
      <Card>
        <div className="space-y-4 p-6">
          <Heading title="Mixpanel Admin Interface" size="h2" />
          <div>
            This interface allows super users to manually track events and set user properties in Mixpanel.
            Use with caution as this directly affects your analytics data.
          </div>

          {error && <Alert variant="error" title="Error">{error}</Alert>}
          {success && <Alert variant="success" title="Success">{success}</Alert>}

          <div>
            <Button onClick={checkStatus} disabled={loading}>
              {loading ? <Spinner /> : 'Check Status'}
            </Button>

            {status && (
              <div style={{ marginTop: '1rem', padding: '1rem', backgroundColor: '#f5f5f5', borderRadius: '4px' }}>
                <div><strong>Status:</strong> {status.initialized ? 'Initialized' : 'Not Initialized'}</div>
                <div><strong>Last Check:</strong> {new Date(status.timestamp).toLocaleString()}</div>
                <div><strong>Admin:</strong> {status.admin}</div>
              </div>
            )}
          </div>
        </div>
      </Card>

      {/* Track Event Form */}
      <Card>
        <div className="space-y-4 p-6">
          <Heading title="Track Event" size="h3" />
          <form onSubmit={handleTrackEvent} className="space-y-4">
            <div>
              <label htmlFor="track-userId" className="block text-sm font-medium mb-1">User ID:</label>
              <input
                id="track-userId"
                type="text"
                value={trackEventForm.userId}
                onChange={(e) => setTrackEventForm({...trackEventForm, userId: e.target.value})}
                required
                className="w-full p-2 border border-gray-300 rounded-md"
              />
            </div>

            <div>
              <label htmlFor="track-eventName" className="block text-sm font-medium mb-1">Event Name:</label>
              <input
                id="track-eventName"
                type="text"
                value={trackEventForm.eventName}
                onChange={(e) => setTrackEventForm({...trackEventForm, eventName: e.target.value})}
                required
                className="w-full p-2 border border-gray-300 rounded-md"
              />
            </div>

            <div>
              <label htmlFor="track-properties" className="block text-sm font-medium mb-1">Properties (JSON):</label>
              <textarea
                id="track-properties"
                value={trackEventForm.properties}
                onChange={(e) => setTrackEventForm({...trackEventForm, properties: e.target.value})}
                rows={3}
                className="w-full p-2 border border-gray-300 rounded-md"
              />
            </div>

            <Button type="submit" disabled={loading}>
              {loading ? <Spinner /> : 'Track Event'}
            </Button>
          </form>
        </div>
      </Card>

      {/* Set Properties Form */}
      <Card>
        <div className="space-y-4 p-6">
          <Heading title="Set User Properties" size="h3" />
          <form onSubmit={handleSetProperties} className="space-y-4">
            <div>
              <label htmlFor="props-userId" className="block text-sm font-medium mb-1">User ID:</label>
              <input
                id="props-userId"
                type="text"
                value={setPropertiesForm.userId}
                onChange={(e) => setSetPropertiesForm({...setPropertiesForm, userId: e.target.value})}
                required
                className="w-full p-2 border border-gray-300 rounded-md"
              />
            </div>

            <div>
              <label htmlFor="props-properties" className="block text-sm font-medium mb-1">Properties (JSON):</label>
              <textarea
                id="props-properties"
                value={setPropertiesForm.properties}
                onChange={(e) => setSetPropertiesForm({...setPropertiesForm, properties: e.target.value})}
                rows={4}
                required
                className="w-full p-2 border border-gray-300 rounded-md"
              />
            </div>

            <Button type="submit" disabled={loading}>
              {loading ? <Spinner /> : 'Set Properties'}
            </Button>
          </form>
        </div>
      </Card>

      {/* Increment Property Form */}
      <Card>
        <div className="space-y-4 p-6">
          <Heading title="Increment Property" size="h3" />
          <form onSubmit={handleIncrement} className="space-y-4">
            <div>
              <label htmlFor="inc-userId" className="block text-sm font-medium mb-1">User ID:</label>
              <input
                id="inc-userId"
                type="text"
                value={incrementForm.userId}
                onChange={(e) => setIncrementForm({...incrementForm, userId: e.target.value})}
                required
                className="w-full p-2 border border-gray-300 rounded-md"
              />
            </div>

            <div>
              <label htmlFor="inc-property" className="block text-sm font-medium mb-1">Property Name:</label>
              <input
                id="inc-property"
                type="text"
                value={incrementForm.property}
                onChange={(e) => setIncrementForm({...incrementForm, property: e.target.value})}
                required
                className="w-full p-2 border border-gray-300 rounded-md"
              />
            </div>

            <div>
              <label htmlFor="inc-value" className="block text-sm font-medium mb-1">Increment Value:</label>
              <input
                id="inc-value"
                type="number"
                value={incrementForm.value}
                onChange={(e) => setIncrementForm({...incrementForm, value: parseInt(e.target.value) || 1})}
                className="w-full p-2 border border-gray-300 rounded-md"
              />
            </div>

            <Button type="submit" disabled={loading}>
              {loading ? <Spinner /> : 'Increment Property'}
            </Button>
          </form>
        </div>
      </Card>
    </div>
  );
};
