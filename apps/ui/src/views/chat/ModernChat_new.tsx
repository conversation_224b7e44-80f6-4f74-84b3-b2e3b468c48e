import { useCallback, useState, useRef, useEffect, useContext } from "react";
import { LocationContext } from "../../contexts";
import { PreferencesContext } from "../../ui/PreferencesContext";
import useAuth from "../../hooks/useAuth";
import { useTranslation } from "react-i18next";
import dayjs from "dayjs";
import relativeTime from "dayjs/plugin/relativeTime";
import ReactMarkdown from "react-markdown";
import Modal from "../../ui/Modal";
import Button from "../../ui/Button";
import {
  chatService,
  Chat as ApiChat,
  Message as ApiMessage,
} from "../../api/chat";
import rehypeRaw from "rehype-raw";
import api from "../../api";
// UI components and icons
import {
  SendIcon,
  PlusIcon,
  EditIcon,
  TrashIcon,
  MenuIcon,
} from "../../ui/icons";
import {
  FaThumbsUp,
  FaThumbsDown,
  FaDownload,
  FaEllipsisV,
  FaPaperPlane,
  FaInfoCircle,
} from "react-icons/fa";

// Special content components
import ChatSpecialContent from "../../ui/ChatSpecialContent";
import SVGGraphRenderer from "../../ui/SVGGraphRenderer";
import TableRenderer from "../../ui/TableRenderer";
import ProductTable from "../../ui/ProductTable";

import SmokeyIcon from "../../assets/smokey_icon.png";
import "./modernChat.css";
// Assets and utilities

// Import the new hook
import { useAgentBasedQuestions } from "../../hooks/useAgentBasedQuestions";

// Add the relativeTime plugin
dayjs.extend(relativeTime);

// Type definitions
export interface Chat {
  chat_id: string;
  name: string;
  created_at: string;
  agents?: Agent[];
  summary?: {
    title: string;
    description: string;
    topics: string[];
  };
  insights?: Array<{
    title: string;
    description: string;
    impact: "high" | "medium" | "low";
    type: "automation" | "campaign" | "general";
  }>;
}

export interface Agent {
  id: string;
  name: string;
  role: string;
  description: string;
  icon: string;
  capabilities: string[];
}

export interface Message {
  id: string;
  content: string;
  role: "user" | "assistant";
  timestamp: string;
  agent?: Agent;
  chat?: Chat;
}

// New interface for API message response
export interface ApiMessageResponse {
  id: number;
  content: string;
  role: "user" | "assistant";
  timestamp: string;
  chat_id: string;
  agent_id: string | null;
  metadata: {
    agent_name?: string;
    agent_role?: string;
    selected_by?: string;
  } | null;
  data?: any;
}

// New interface for the API response format
export interface MessageResponse {
  chat: {
    id: string;
    chat_id: string;
    name: string;
    created_at: string;
    updated_at: string;
    location_id: string;
    agent_ids: number | number[];
    status: string;
    metadata: Record<string, any>;
    agents: Agent[];
  };
  messages: ApiMessageResponse[];
  // Keep legacy fields for backward compatibility
  id?: string;
  content?: string;
  timestamp?: string;
  agent_id?: string;
  metadata?: Record<string, any>;
  data?: any;
}

export interface Product {
  id: string;
  name: string;
  category: string;
  price: number;
  thc_percentage?: number;
  cbd_percentage?: number;
  image_url?: string;
  description?: string;
  strain_type?: string;
  effects?: string[];
  inventory_count?: number;
}

export interface ChatMessage {
  message_id: string;
  role: "human" | "ai" | "user" | "assistant";
  content: string;
  timestamp: string;
  chat_id: string;
  agent_id?: string;
  feedback?: "like" | "dislike";
  data?: {
    type?:
      | "insight"
      | "automation"
      | "plan" // New type for automation plans
      | "step"
      | "products"
      | "productType"
      | "feelings"
      | "graph"
      | "image"
      | "table";
    insight?: any;
    plan?: any;
    step?: any;
    products?: Product[];
    productTypes?: Array<{
      name: string;
      description: string;
      image: string;
    }>;
    feelings?: Array<{
      name: string;
      description?: string;
      image?: string;
    }>;
    graph?: {
      svgContent?: string;
      svgUrl?: string;
      graphData?: any;
      title?: string;
      type?: "chart" | "table";
    };
    images?: Array<{
      url: string;
      prompt: string;
      path: string;
    }>;
    table?: {
      title?: string;
      headers: string[];
      rows: string[][];
    };
    query_type?: string;
    prompt?: string;
    error_messages?: string[];
    suggested_next_questions?: string[];
    sales_data?: any[];
    sales_data_type?: string;
    agent?: {
      name: string;
      role?: string;
    };
  };
  specialType?: string;
  isStreaming?: boolean;
  error?: boolean;
  metadata?: Record<string, any>;
}

// Helper functions
const formatMessageTime = (dateString: string) => {
  try {
    if (!dateString) return "";
    const date = new Date(dateString);
    if (isNaN(date.getTime())) return "";
    return date.toLocaleTimeString([], { hour: "2-digit", minute: "2-digit" });
  } catch (error) {
    console.error("Error formatting date:", error);
    return "";
  }
};

const extractImageUrls = (markdown: string): string[] => {
  const imageRegex = /!\[.*?\]\((.*?)\)/g;
  const urls: string[] = [];
  let match;

  while ((match = imageRegex.exec(markdown)) !== null) {
    if (match[1]) urls.push(match[1]);
  }
  return urls;
};

// Component for typing indicator
const TypingIndicator = () => (
  <div className="typing-indicator">
    <span></span>
    <span></span>
    <span></span>
  </div>
);

// Loading spinner component
const LoadingSpinner = () => (
  <div className="loading-spinner">
    <div className="spinner"></div>
  </div>
);

// Array of greeting phrases
const greetingPhrases = [
  {
    greeting: "What's up, bud?",
    subtitle: "Your cannabis marketing assistant is ready to roll.",
  },
  {
    greeting: "High there!",
    subtitle: "Let's elevate your cannabis marketing strategy today.",
  },
  {
    greeting: "How's it growing?",
    subtitle: "Ask me anything about your cannabis business needs.",
  },
];

// Create an X icon component for closing the sidebar
const XIcon = () => (
  <svg
    xmlns="http://www.w3.org/2000/svg"
    width="24"
    height="24"
    viewBox="0 0 24 24"
    fill="none"
    stroke="currentColor"
    strokeWidth="2"
    strokeLinecap="round"
    strokeLinejoin="round"
  >
    <line x1="18" y1="6" x2="6" y2="18"></line>
    <line x1="6" y1="6" x2="18" y2="18"></line>
  </svg>
);

// Create a separate component for chat list
const ChatListModal = ({
  chats,
  currentChat,
  onSelectChat,
  onNewChat,
}: {
  chats: any[];
  currentChat: Chat | null;
  onSelectChat: (chat: any) => void;
  onNewChat: () => void;
}) => {
  const { t } = useTranslation();
  return (
    <div className="chat-history-modal">
      <Button onClick={onNewChat}>{t("new_chat")}</Button>
      <div className="chat-list-modal">
        {chats.map((chat, index) => (
          <div
            key={index}
            className={`chat-item ${
              currentChat?.chat_id === chat.chat_id ? "active" : ""
            }`}
            onClick={() => onSelectChat(chat)}
          >
            <div className="chat-name">{chat.name}</div>
            <div className="chat-time">{dayjs(chat.created_at).fromNow()}</div>
          </div>
        ))}
      </div>
    </div>
  );
};

export default function ModernChat() {
  const { t } = useTranslation();
  const [preferences] = useContext(PreferencesContext);
  const { user } = useAuth();
  const [location] = useContext(LocationContext);
  const messagesEndRef = useRef<HTMLDivElement>(null);

  // State hooks
  const [messages, setMessages] = useState<ChatMessage[]>([]);
  const [inputValue, setInputValue] = useState("");
  const [isLoading, setIsLoading] = useState(false);
  const [currentChat, setCurrentChat] = useState<Chat | null>(null);
  const [chats, setChats] = useState<any[]>([]);
  const [isRenameModalOpen, setIsRenameModalOpen] = useState(false);
  const [newChatName, setNewChatName] = useState("");
  const [isPageLoading, setIsPageLoading] = useState(false);
  const [isMessagesLoading, setIsMessagesLoading] = useState(false);
  const [feedbackGiven, setFeedbackGiven] = useState<{
    [key: string]: "like" | "dislike" | null;
  }>({});
  const [showSidebar, setShowSidebar] = useState(false);
  const [windowWidth, setWindowWidth] = useState(window.innerWidth);
  const [hoveredChat, setHoveredChat] = useState<string | null>(null);
  const [contextMenu, setContextMenu] = useState({
    x: 0,
    y: 0,
    chatId: null as string | null,
    isOpen: false,
  });

  // Add state to track if we should use simplified layout
  const [useSimpleLayout, setUseSimpleLayout] = useState(false);

  // State for handling special content types
  const [wizardSelections, setWizardSelections] = useState<{
    [key: string]: boolean;
  }>({});

  // Add state for agent availability
  const [agentAvailability, setAgentAvailability] = useState<any>(null);

  // Add state for showing agent unavailable notification
  const [showAgentNotification, setShowAgentNotification] = useState<{
    show: boolean;
    agentStatus: any;
  }>({ show: false, agentStatus: null });

  // Use the new dynamic question generation hook
  const { questions: thoughtBubbles, loading: questionsLoading } =
    useAgentBasedQuestions();

  // Helper function to check if an agent is available
  const getAgentStatus = (agentId: string) => {
    if (!agentAvailability) return { available: true, agent: null };

    // Check if agent is in available list
    const availableAgent = agentAvailability.available?.find(
      (a: any) => a.agentId === agentId
    );
    if (availableAgent) return { available: true, agent: availableAgent };

    // Check if agent is unavailable
    const unavailableAgent = agentAvailability.unavailable?.find(
      (a: any) => a.agentId === agentId
    );
    if (unavailableAgent) return { available: false, agent: unavailableAgent };

    // Check if agent is partially available
    const partialAgent = agentAvailability.partial?.find(
      (a: any) => a.agentId === agentId
    );
    if (partialAgent)
      return { available: true, agent: partialAgent, partial: true };

    return { available: true, agent: null }; // Default to available if not found
  };

  // Component for agent unavailable notification
  const AgentUnavailableNotification = ({
    agentStatus,
    onDismiss,
  }: {
    agentStatus: { available: boolean; agent: any; partial?: boolean };
    onDismiss: () => void;
  }) => {
    if (agentStatus.available) return null;

    return (
      <div className="agent-unavailable-notification">
        <div className="notification-content">
          <FaInfoCircle className="notification-icon" />
          <div className="notification-text">
            <strong>
              {agentStatus.agent?.name || "This agent"} is currently
              unavailable.
            </strong>
            <p>Missing: {agentStatus.agent?.missingRequirements?.join(", ")}</p>
            <p>
              Your message will be handled by the general assistant instead.
            </p>
          </div>
          <button className="notification-dismiss" onClick={onDismiss}>
            ×
          </button>
        </div>
      </div>
    );
  };

  // Initialize conversation starters and greeting
  const [randomGreeting] = useState(
    greetingPhrases[Math.floor(Math.random() * greetingPhrases.length)]
  );

  // Check on initial load and window resize
  useEffect(() => {
    const handleResize = () => {
      setWindowWidth(window.innerWidth);
      // Only initially hide sidebar on small screens
      if (window.innerWidth < 768) {
        setShowSidebar(false);
        setUseSimpleLayout(true); // Use simple layout on mobile
      } else {
        setUseSimpleLayout(false); // Use sidebar layout on tablet+
      }
    };

    // Call once on initial load
    handleResize();

    window.addEventListener("resize", handleResize);
    return () => window.removeEventListener("resize", handleResize);
  }, []);

  // Fetch user chats on component mount
  useEffect(() => {
    const loadChats = async () => {
      try {
        if (!user) return;
        setIsPageLoading(true);
        if (location && location.id) {
          const chatsData = await chatService.getChats();
          // Cast the response to our local Chat type
          setChats(
            chatsData.map(
              (chat) =>
                ({
                  chat_id: chat.chat_id,
                  name: chat.name,
                  created_at: chat.created_at,
                  agents: chat.agents,
                } as Chat)
            )
          );
          if (chatsData.length > 0) {
            setShowSidebar(true);
          }
        }
      } catch (error) {
        console.error("Error loading chats:", error);
      } finally {
        setIsPageLoading(false);
      }
    };

    const loadAgentAvailability = async () => {
      try {
        if (location && location.id) {
          const availability = await api.agents.getAvailability(location.id);
          setAgentAvailability(availability);
        }
      } catch (error) {
        console.error("Error loading agent availability:", error);
      }
    };

    loadChats();
    loadAgentAvailability();
  }, [user, location]);

  // Load messages for selected chat
  const loadMessages = useCallback(async (chatId: string) => {
    try {
      setIsMessagesLoading(true);

      const messagesData = await chatService.getChatMessages(chatId);
      // Map backend message format to frontend format
      const formattedMessages = messagesData.map(
        (msg: any) =>
          ({
            message_id: msg.id || msg.message_id,
            role: msg.role === "user" ? "human" : "ai",
            content: msg.content,
            timestamp: msg.timestamp,
            chat_id: msg.chat_id || chatId,
            agent_id: msg.agent_id,
            metadata: msg.metadata,
            data: msg.data,
          } as ChatMessage)
      );

      // Sort messages by timestamp in ascending order (oldest first)
      const sortedMessages = formattedMessages.sort(
        (a, b) =>
          new Date(a.timestamp).getTime() - new Date(b.timestamp).getTime()
      );

      setMessages(sortedMessages);
    } catch (error) {
      console.error("Error loading messages:", error);
    } finally {
      setIsMessagesLoading(false);
    }
  }, []);

  // Scroll to bottom when messages change
  useEffect(() => {
    messagesEndRef.current?.scrollIntoView({ behavior: "smooth" });
  }, [messages]);

  // Handle chat selection
  const handleChatSelect = async (chat: Chat) => {
    setIsPageLoading(true);
    setCurrentChat(chat);
    await loadMessages(chat.chat_id);
    setIsPageLoading(false);

    // Close sidebar on mobile after selection
    if (windowWidth < 768) {
      setShowSidebar(false);
    }
  };

  // Handle creating a new chat
  const handleNewChat = () => {
    setCurrentChat(null);
    setMessages([]);

    // Close sidebar on mobile
    if (windowWidth < 768) {
      setShowSidebar(false);
    }
  };

  // Handle renaming a chat
  const handleRenameChat = async () => {
    if (currentChat && newChatName.trim()) {
      try {
        await chatService.renameChat(currentChat.chat_id, newChatName);

        // Update chat name locally
        setChats((prevChats) =>
          prevChats.map((chat) =>
            chat.chat_id === currentChat.chat_id
              ? { ...chat, name: newChatName }
              : chat
          )
        );
        setCurrentChat({
          ...currentChat,
          name: newChatName,
        });
        setIsRenameModalOpen(false);
        setNewChatName("");
      } catch (error) {
        console.error("Error renaming chat:", error);
      }
    }
  };

  // Open rename modal
  const openRenameModal = () => {
    if (currentChat) {
      setNewChatName(currentChat.name);
      setIsRenameModalOpen(true);
    }
  };

  // Handle deleting a chat
  const handleDeleteChat = async () => {
    if (currentChat) {
      if (window.confirm(t("delete_chat_confirmation"))) {
        try {
          // Since deleteChat doesn't exist, use a POST request instead
          await chatService.renameChat(currentChat.chat_id, "__deleted__");

          // Remove chat from the list
          setChats((prevChats) =>
            prevChats.filter((chat) => chat.chat_id !== currentChat.chat_id)
          );
          // Reset current chat and messages
          setCurrentChat(null);
          setMessages([]);
        } catch (error) {
          console.error("Error deleting chat:", error);
        }
      }
    }
  };

  // Ref for the textarea to handle auto-resize
  const textareaRef = useRef<HTMLTextAreaElement>(null);

  // Handle input changes to detect @mentions and check agent availability
  const handleInputChange = (value: string) => {
    setInputValue(value);

    // Auto-resize textarea
    if (textareaRef.current) {
      textareaRef.current.style.height = 'auto';
      const newHeight = Math.min(textareaRef.current.scrollHeight, 120);
      textareaRef.current.style.height = `${newHeight}px`;
      textareaRef.current.style.overflowY = newHeight >= 120 ? 'auto' : 'hidden';
    }

    // Check for @mentions
    const mentionMatch = value.match(/@(\w+)/);
    if (mentionMatch) {
      const mentionedName = mentionMatch[1].toLowerCase();
      const agentNameToId: { [key: string]: string } = {
        smokey: "1",
        craig: "2",
        pops: "3",
        ezal: "4",
        "money mike": "5",
        "mrs. parker": "6",
        "mrs parker": "6",
        parker: "6",
        deebo: "7",
      };

      const agentId = agentNameToId[mentionedName];
      if (agentId) {
        const agentStatus = getAgentStatus(agentId);
        if (!agentStatus.available) {
          setShowAgentNotification({ show: true, agentStatus });
          return;
        }
      }
    }

    // Clear notification if no mention or agent is available
    setShowAgentNotification({ show: false, agentStatus: null });
  };

  // Send a new message
  const handleSendMessage = async () => {
    if (inputValue.trim() === "") return;

    const messageContent = inputValue.trim();
    setInputValue("");

    // Reset textarea height
    if (textareaRef.current) {
      textareaRef.current.style.height = 'auto';
    }

    // Dismiss any active notifications
    setShowAgentNotification({ show: false, agentStatus: null });

    // Add user message immediately
    const userMessage: ChatMessage = {
      message_id: Date.now().toString() + "_user",
      role: "human",
      content: messageContent,
      timestamp: new Date().toISOString(),
      chat_id: currentChat?.chat_id || "",
    };

    setMessages((prev) => [...prev, userMessage]);

    // Add loading placeholder for AI response
    const responseMessageId = Date.now().toString() + "_ai";
    const loadingMessage: ChatMessage = {
      message_id: responseMessageId,
      role: "ai",
      content: "",
      timestamp: new Date().toISOString(),
      chat_id: currentChat?.chat_id || "",
      isStreaming: true,
    };

    setMessages((prev) => [...prev, loadingMessage]);

    try {
      // Get location ID from URL
      const locationId = window.location.pathname.split("/")[2];

      // Let the backend handle agent selection based on intent analysis
      // Only detect @mentions to pass specific agent requests, otherwise use default
      let agentId = "1"; // Default to SMOKEY

      // Check for explicit @mentions in the message
      const mentionMatch = messageContent.match(/@(\w+)/);
      if (mentionMatch) {
        const mentionedName = mentionMatch[1].toLowerCase();
        // Map common agent names/aliases to IDs
        const agentNameToId: { [key: string]: string } = {
          smokey: "1",
          craig: "2",
          pops: "3",
          ezal: "4",
          "money mike": "5",
          "mrs. parker": "6",
          "mrs parker": "6",
          parker: "6",
          deebo: "7",
        };

        agentId = agentNameToId[mentionedName] || "1";

        if (agentNameToId[mentionedName]) {
          console.log(
            `Agent mention detected: ${mentionedName} -> Agent ${agentId}`
          );
        }
      }

      // Send the message to the backend - let it handle agent selection based on intent
      const response = await chatService.sendMessage({
        message: messageContent,
        agentId, // This is just a hint, backend will override based on intent analysis
        chatId: currentChat?.chat_id || undefined,
        locationId,
      });

      // Update with the final AI response
      setMessages((prev) => {
        const updatedMessages = [...prev];
        const loadingIndex = updatedMessages.findIndex(
          (msg) => msg.message_id === responseMessageId
        );

        if (loadingIndex !== -1) {
          // Handle different response formats by first casting to unknown
          const aiResponse = response as unknown as any;

          // Check if we have a messages array in the response (new format)
          if (
            aiResponse &&
            aiResponse.messages &&
            aiResponse.messages.length > 0
          ) {
            // Get the last message which should be the assistant's response
            const assistantMessage = aiResponse.messages.find(
              (msg: any) => msg.role === "assistant"
            );

            if (assistantMessage) {
              const finalMessage: ChatMessage = {
                message_id:
                  (assistantMessage.id || "").toString() || responseMessageId,
                role: "ai",
                content: assistantMessage.content || "",
                timestamp:
                  assistantMessage.timestamp || new Date().toISOString(),
                isStreaming: false,
                chat_id: aiResponse.chat?.chat_id || "",
                data: assistantMessage.data,
                agent_id: assistantMessage.agent_id || undefined,
                metadata: assistantMessage.metadata || {},
              };
              updatedMessages[loadingIndex] = finalMessage;
            }
          } else if (aiResponse) {
            // Fallback to the old format for backward compatibility
            const finalMessage: ChatMessage = {
              message_id: (aiResponse.id || "").toString() || responseMessageId,
              role: "ai",
              content: aiResponse.content || "",
              timestamp: aiResponse.timestamp || new Date().toISOString(),
              isStreaming: false,
              chat_id: aiResponse.chat?.chat_id || "",
              data: aiResponse.data,
              agent_id: aiResponse.agent_id || undefined,
              metadata: aiResponse.metadata || {},
            };
            updatedMessages[loadingIndex] = finalMessage;
          }
        }
        return updatedMessages;
      });

      // Update current chat if this is a new conversation
      if (!currentChat && response.chat) {
        // Cast the API chat to our local Chat type
        setCurrentChat({
          chat_id: response.chat.chat_id,
          name: response.chat.name,
          created_at: response.chat.created_at,
          agents: response.chat.agents,
        } as Chat);

        // Update chat list
        const chatsData = await chatService.getChats();
        setChats(
          chatsData.map(
            (chat) =>
              ({
                chat_id: chat.chat_id,
                name: chat.name,
                created_at: chat.created_at,
                agents: chat.agents,
              } as Chat)
          )
        );
      } else {
        // Refresh the chat list to get updated chat names
        // This ensures we have the latest chat names after auto-generation
        const chatsData = await chatService.getChats();
        setChats(
          chatsData.map(
            (chat) =>
              ({
                chat_id: chat.chat_id,
                name: chat.name,
                created_at: chat.created_at,
                agents: chat.agents,
              } as Chat)
          )
        );

        // If we have a current chat, update it with the latest data
        if (currentChat) {
          const updatedChat = chatsData.find(
            (chat) => chat.chat_id === currentChat.chat_id
          );
          if (updatedChat) {
            setCurrentChat({
              chat_id: updatedChat.chat_id,
              name: updatedChat.name,
              created_at: updatedChat.created_at,
              agents: updatedChat.agents,
            } as Chat);
          }
        }
      }
    } catch (error: any) {
      console.error("Error sending message:", error);
      console.log("Error type:", typeof error);
      console.log("Error name:", error.name);
      console.log("Error message:", error.message);
      console.log("Error status:", error.status);
      console.log("Error response:", error.response);

      // Determine appropriate error message based on error type
      let errorMessage = "Sorry, I encountered an error. Please try again.";

      // Check if this is an ApiError with agent availability message
      if (
        error.message?.includes("Agent not available") ||
        error.message?.includes("required integrations missing")
      ) {
        errorMessage = `No product data available. Please import the product data first to use this feature.`;
        console.log("Setting custom error message:", errorMessage);
      } else if (
        error.response?.status === 400 ||
        error.response?.status === 404
      ) {
        const errorData = error.response?.data;
        console.log("Processing error data:", errorData);

        // Check if this is an agent availability error
        if (
          errorData?.error?.message?.includes("Agent not available") ||
          errorData?.error?.message?.includes("required integrations missing")
        ) {
          errorMessage = `No product data available. Please import the product data first to use this feature.`;
          console.log("Setting custom error message:", errorMessage);
        } else if (errorData?.message?.includes("Agent not available")) {
          errorMessage =
            "This agent is currently unavailable. Please try a different agent or contact your administrator.";
        } else if (error.response?.status === 404) {
          errorMessage =
            "The requested resource was not found. Please try again.";
        }
      } else if (error.response?.status >= 500) {
        errorMessage = "Server error occurred. Please try again in a moment.";
      }

      console.log("Final error message:", errorMessage);

      // Update the loading message with error state
      setMessages((prev) => {
        const updatedMessages = [...prev];
        const loadingIndex = updatedMessages.findIndex(
          (msg) => msg.message_id === responseMessageId
        );

        if (loadingIndex !== -1) {
          updatedMessages[loadingIndex] = {
            message_id: responseMessageId,
            role: "ai",
            content: errorMessage,
            timestamp: new Date().toISOString(),
            isStreaming: false,
            error: true,
            chat_id: currentChat?.chat_id || "",
          };
        }
        return updatedMessages;
      });
    } finally {
      setIsLoading(false);
    }
  };

  // Add feedback handler
  const handleFeedback = async (
    messageId: string,
    feedbackType: "like" | "dislike"
  ) => {
    try {
      // Update local state immediately for responsiveness
      setFeedbackGiven((prev) => ({ ...prev, [messageId]: feedbackType }));

      // Update feedback locally only - API doesn't support feedback yet
      console.log("Feedback recorded locally:", messageId, feedbackType);
    } catch (error) {
      console.error("Error saving feedback:", error);
    }
  };

  // Handle image download
  const handleImageDownload = (content: string) => {
    const imageUrls = extractImageUrls(content);
    imageUrls.forEach((url) => {
      const link = document.createElement("a");
      link.href = url;
      link.download = url.split("/").pop() || "image.png";
      link.target = "_blank";
      link.rel = "noopener noreferrer";
      document.body.appendChild(link);
      link.click();
      document.body.removeChild(link);
    });
  };

  // Handle special content interactions
  const handleProductClick = (product: any) => {
    console.log("Product clicked:", product);
    // Implement product click behavior
  };

  const handleProductTypeSelect = (productType: any) => {
    // Create a new user message with the selected product type
    const userMessage: ChatMessage = {
      message_id: Date.now().toString(),
      role: "human",
      content: `I'm interested in ${productType.name}`,
      timestamp: new Date().toISOString(),
      chat_id: currentChat?.chat_id || "",
    };

    setMessages((prev) => [...prev, userMessage]);

    // Trigger a new AI response based on the selection
    handleSendSpecialMessage(`Show me ${productType.name} products`);
  };

  const handleFeelingsSelect = (feelings: string[]) => {
    // Create a new user message with the selected feelings
    const userMessage: ChatMessage = {
      message_id: Date.now().toString(),
      role: "human",
      content: `I want to feel: ${feelings.join(", ")}`,
      timestamp: new Date().toISOString(),
      chat_id: currentChat?.chat_id || "",
    };

    setMessages((prev) => [...prev, userMessage]);

    // Trigger a new AI response based on the selection
    handleSendSpecialMessage(
      `Show me products that make me feel ${feelings.join(", ")}`
    );
  };

  // Helper function to send a message without user input
  const handleSendSpecialMessage = async (content: string) => {
    const messageId = Date.now().toString();
    const responseMessageId = `${messageId}_response`;

    // Create initial loading message for the AI response
    const loadingMessage: ChatMessage = {
      message_id: responseMessageId,
      role: "ai",
      content: "",
      timestamp: new Date().toISOString(),
      isStreaming: true,
      chat_id: currentChat?.chat_id || "",
    };

    // Add loading message to chat
    setMessages((prev) => [...prev, loadingMessage]);
    setIsLoading(true);

    try {
      // Get location ID from URL
      const locationId = window.location.pathname.split("/")[2];

      // Send the message to the backend
      const response = await chatService.sendMessage({
        message: content,
        agentId: "1", // Using default agent
        chatId: currentChat?.chat_id || undefined,
        locationId, // Add the required locationId parameter
      });

      // Update with the final AI response
      setMessages((prev) => {
        const updatedMessages = [...prev];
        const loadingIndex = updatedMessages.findIndex(
          (msg) => msg.message_id === responseMessageId
        );

        if (loadingIndex !== -1) {
          // Handle different response formats by first casting to unknown
          const aiResponse = response as unknown as any;

          // Check if we have a messages array in the response (new format)
          if (
            aiResponse &&
            aiResponse.messages &&
            aiResponse.messages.length > 0
          ) {
            // Get the last message which should be the assistant's response
            const assistantMessage = aiResponse.messages.find(
              (msg: any) => msg.role === "assistant"
            );

            if (assistantMessage) {
              const finalMessage: ChatMessage = {
                message_id:
                  (assistantMessage.id || "").toString() || responseMessageId,
                role: "ai",
                content: assistantMessage.content || "",
                timestamp:
                  assistantMessage.timestamp || new Date().toISOString(),
                isStreaming: false,
                chat_id: aiResponse.chat?.chat_id || "",
                data: assistantMessage.data,
                agent_id: assistantMessage.agent_id || undefined,
                metadata: assistantMessage.metadata || {},
              };
              updatedMessages[loadingIndex] = finalMessage;
            }
          } else if (aiResponse) {
            // Fallback to the old format for backward compatibility
            const finalMessage: ChatMessage = {
              message_id: (aiResponse.id || "").toString() || responseMessageId,
              role: "ai",
              content: aiResponse.content || "",
              timestamp: aiResponse.timestamp || new Date().toISOString(),
              isStreaming: false,
              chat_id: aiResponse.chat?.chat_id || "",
              data: aiResponse.data,
              agent_id: aiResponse.agent_id || undefined,
              metadata: aiResponse.metadata || {},
            };
            updatedMessages[loadingIndex] = finalMessage;
          }
        }
        return updatedMessages;
      });

      // Update current chat if this is a new conversation
      if (!currentChat && response.chat) {
        // Cast the API chat to our local Chat type
        setCurrentChat({
          chat_id: response.chat.chat_id,
          name: response.chat.name,
          created_at: response.chat.created_at,
          agents: response.chat.agents,
        } as Chat);

        // Update chat list
        const chatsData = await chatService.getChats();
        setChats(
          chatsData.map(
            (chat) =>
              ({
                chat_id: chat.chat_id,
                name: chat.name,
                created_at: chat.created_at,
                agents: chat.agents,
              } as Chat)
          )
        );
      }
    } catch (error: any) {
      console.error("Error sending message:", error);

      // Determine appropriate error message based on error type
      let errorMessage = "Sorry, I encountered an error. Please try again.";

      if (error.response?.status === 400 || error.response?.status === 404) {
        const errorData = error.response?.data;

        // Check if this is an agent availability error
        if (
          errorData?.error?.message?.includes("Agent not available") ||
          errorData?.error?.message?.includes("required integrations missing")
        ) {
          errorMessage = `No product data available. Please import the product data first to use this feature.`;
        } else if (errorData?.message?.includes("Agent not available")) {
          errorMessage =
            "This agent is currently unavailable. Please try a different agent or contact your administrator.";
        } else if (error.response?.status === 404) {
          errorMessage =
            "The requested resource was not found. Please try again.";
        }
      } else if (error.response?.status >= 500) {
        errorMessage = "Server error occurred. Please try again in a moment.";
      }

      // Update the loading message with error state
      setMessages((prev) => {
        const updatedMessages = [...prev];
        const loadingIndex = updatedMessages.findIndex(
          (msg) => msg.message_id === responseMessageId
        );

        if (loadingIndex !== -1) {
          updatedMessages[loadingIndex] = {
            message_id: responseMessageId,
            role: "ai",
            content: errorMessage,
            timestamp: new Date().toISOString(),
            isStreaming: false,
            error: true,
            chat_id: currentChat?.chat_id || "",
          };
        }
        return updatedMessages;
      });
    } finally {
      setIsLoading(false);
    }
  };

  // Context menu handlers
  const handleContextMenu = (e: React.MouseEvent, chatId: string) => {
    e.preventDefault();
    e.stopPropagation();
    setContextMenu({
      x: e.clientX,
      y: e.clientY,
      chatId,
      isOpen: true,
    });
  };

  const closeContextMenu = useCallback(() => {
    setContextMenu({ ...contextMenu, isOpen: false });
  }, [contextMenu]);

  // Close context menu on outside click
  useEffect(() => {
    const handleClickOutside = () => {
      if (contextMenu.isOpen) {
        closeContextMenu();
      }
    };

    document.addEventListener("click", handleClickOutside);
    return () => {
      document.removeEventListener("click", handleClickOutside);
    };
  }, [contextMenu.isOpen, closeContextMenu]);

  // Function for welcome option clicks
  const handleWelcomeOptionClick = (prompt: string) => {
    setInputValue(prompt);
    handleSendMessage();
  };

  // Render message content based on type
  const renderMessageContent = (message: ChatMessage) => {
    // Determine if message has special content types
    const hasSpecialType = message.data?.type !== undefined;
    const isProductsContent =
      message.data?.type === "products" ||
      message.specialType === "products" ||
      (message.data?.products && message.data.products.length > 0);

    const isProductTypeContent =
      message.data?.type === "productType" ||
      message.specialType === "productType" ||
      (message.data?.productTypes && message.data.productTypes.length > 0);

    const isFeelingsContent =
      message.data?.type === "feelings" ||
      message.specialType === "feelings" ||
      (message.data?.feelings && message.data.feelings.length > 0);

    const isGraphContent =
      message.data?.type === "graph" ||
      message.specialType === "graph" ||
      message.data?.graph?.svgContent ||
      message.data?.graph?.graphData;

    const isImageContent =
      message.data?.type === "image" || message.data?.images;
    const hasTableData =
      message.data?.type === "table" ||
      (message.content &&
        typeof message.content === "string" &&
        message.content.includes('"table":'));

    if (message.isStreaming) {
      return (
        <div className="message-content-wrapper">
          {message.content && <ReactMarkdown>{message.content}</ReactMarkdown>}
          <TypingIndicator />
        </div>
      );
    }

    // Handle table data
    if (hasTableData) {
      let tableData:
        | { title?: string; headers: string[]; rows: string[][] }
        | undefined;
      let textContent = message.content;

      try {
        // Try to parse table data from the content
        if (
          typeof message.content === "string" &&
          message.content.includes('"table":')
        ) {
          const parsed = JSON.parse(message.content);
          tableData = parsed.table;
          textContent = parsed.text || "";
        } else if (message.data && "table" in message.data) {
          tableData = message.data.table as {
            title?: string;
            headers: string[];
            rows: string[][];
          };
        }
      } catch (e) {
        console.error("Failed to parse table data:", e);
      }

      return (
        <div className="chat-message-content">
          {textContent && (
            <ReactMarkdown
              components={{
                h1: ({ node, ...props }) => (
                  <h1
                    style={{
                      fontSize: "1.5rem",
                      marginTop: "1rem",
                      marginBottom: "0.5rem",
                      fontWeight: "bold",
                    }}
                    {...props}
                  />
                ),
                h2: ({ node, ...props }) => (
                  <h2
                    style={{
                      fontSize: "1.3rem",
                      marginTop: "1rem",
                      marginBottom: "0.5rem",
                      fontWeight: "bold",
                    }}
                    {...props}
                  />
                ),
                h3: ({ node, ...props }) => (
                  <h3
                    style={{
                      fontSize: "1.2rem",
                      marginTop: "0.75rem",
                      marginBottom: "0.5rem",
                      fontWeight: "bold",
                    }}
                    {...props}
                  />
                ),
                h4: ({ node, ...props }) => (
                  <h4
                    style={{
                      fontSize: "1.1rem",
                      marginTop: "0.75rem",
                      marginBottom: "0.5rem",
                      fontWeight: "bold",
                    }}
                    {...props}
                  />
                ),
                h5: ({ node, ...props }) => (
                  <h5
                    style={{
                      fontSize: "1rem",
                      marginTop: "0.5rem",
                      marginBottom: "0.5rem",
                      fontWeight: "bold",
                    }}
                    {...props}
                  />
                ),
                h6: ({ node, ...props }) => (
                  <h6
                    style={{
                      fontSize: "0.9rem",
                      marginTop: "0.5rem",
                      marginBottom: "0.5rem",
                      fontWeight: "bold",
                    }}
                    {...props}
                  />
                ),
                blockquote: ({ node, ...props }) => (
                  <blockquote
                    style={{
                      borderLeft: "4px solid #ddd",
                      paddingLeft: "1rem",
                      margin: "0.5rem 0",
                      color: "#555",
                    }}
                    {...props}
                  />
                ),
                hr: ({ node, ...props }) => (
                  <hr
                    style={{
                      margin: "1rem 0",
                      border: "none",
                      height: "1px",
                      backgroundColor: "#ddd",
                    }}
                    {...props}
                  />
                ),
                code: ({ node, className, children, ...props }: any) => {
                  const match = /language-(\w+)/.exec(className || "");
                  const isInline = !match && !className;
                  return isInline ? (
                    <code
                      style={{
                        backgroundColor: "#f0f0f0",
                        padding: "0.1rem 0.3rem",
                        borderRadius: "3px",
                        fontSize: "0.9em",
                      }}
                      {...props}
                    >
                      {children}
                    </code>
                  ) : (
                    <code
                      style={{
                        display: "block",
                        backgroundColor: "#f5f5f5",
                        padding: "0.5rem 1rem",
                        borderRadius: "4px",
                        overflowX: "auto",
                        fontSize: "0.9em",
                      }}
                      {...props}
                    >
                      {children}
                    </code>
                  );
                },
              }}
              className="markdown-body"
              rehypePlugins={[rehypeRaw]}
            >
              {textContent}
            </ReactMarkdown>
          )}

          {tableData && (
            <div className="table-container mt-4">
              {tableData.title && (
                <div className="table-title font-semibold mb-2">
                  {tableData.title}
                </div>
              )}
              <table className="min-w-full border-collapse border border-gray-300">
                <thead className="bg-gray-100">
                  <tr>
                    {tableData.headers.map((header: string, i: number) => (
                      <th
                        key={i}
                        className="border border-gray-300 px-4 py-2 text-left"
                      >
                        {header}
                      </th>
                    ))}
                  </tr>
                </thead>
                <tbody>
                  {tableData.rows.map((row: string[], i: number) => (
                    <tr
                      key={i}
                      className={i % 2 === 0 ? "bg-white" : "bg-gray-50"}
                    >
                      {row.map((cell: string, j: number) => (
                        <td
                          key={j}
                          className="border border-gray-300 px-4 py-2"
                        >
                          {cell}
                        </td>
                      ))}
                    </tr>
                  ))}
                </tbody>
              </table>
            </div>
          )}

          {message.data?.suggested_next_questions && (
            <div className="suggested-questions mt-4">
              <div className="text-xs font-semibold mb-2 text-gray-500">
                {t("suggested_questions", "Suggested Questions")}:
              </div>
              <div className="flex flex-wrap gap-2">
                {message.data.suggested_next_questions.map((q: string) => (
                  <button
                    key={q}
                    className="text-xs bg-gray-100 hover:bg-gray-200 rounded-full px-3 py-1"
                    onClick={() => handleWelcomeOptionClick(q)}
                  >
                    {q}
                  </button>
                ))}
              </div>
            </div>
          )}
        </div>
      );
    }

    // Products content
    if (isProductsContent) {
      return (
        <div className="message-content-wrapper">
          <ReactMarkdown>{message.content}</ReactMarkdown>
          <ProductTable
            products={(message.data?.products || []).map((product) => ({
              ...product,
              product_name: product.name,
              image_url: product.image_url || "https://via.placeholder.com/150", // Provide a default image
            }))}
            onProductClick={handleProductClick}
          />
        </div>
      );
    }

    // Product type content
    if (isProductTypeContent) {
      return (
        <div className="message-content-wrapper">
          <ReactMarkdown>{message.content}</ReactMarkdown>
          <ChatSpecialContent
            type="productType"
            productTypes={message.data?.productTypes || []}
            messageId={message.message_id}
            wizardSelections={wizardSelections}
            setWizardSelections={setWizardSelections}
            onProductTypeSelect={handleProductTypeSelect}
          />
        </div>
      );
    }

    // Feelings content
    if (isFeelingsContent) {
      return (
        <div className="message-content-wrapper">
          <ReactMarkdown>{message.content}</ReactMarkdown>
          <ChatSpecialContent
            type="feelings"
            feelings={message.data?.feelings || []}
            messageId={message.message_id}
            wizardSelections={wizardSelections}
            setWizardSelections={setWizardSelections}
            onFeelingsSelect={handleFeelingsSelect}
          />
        </div>
      );
    }

    // Graph content
    if (isGraphContent) {
      return (
        <div className="message-content-wrapper">
          <ReactMarkdown>{message.content}</ReactMarkdown>
          <SVGGraphRenderer
            svgContent={message.data?.graph?.svgContent}
            svgUrl={message.data?.graph?.svgUrl}
            graphData={message.data?.graph?.graphData}
            title={message.data?.graph?.title}
          />
        </div>
      );
    }

    if (isImageContent) {
      return (
        <div className="message-content-wrapper">
          <ReactMarkdown
            components={{
              h1: ({ node, ...props }) => (
                <h1
                  style={{
                    fontSize: "1.5rem",
                    marginTop: "1rem",
                    marginBottom: "0.5rem",
                    fontWeight: "bold",
                  }}
                  {...props}
                />
              ),
              h2: ({ node, ...props }) => (
                <h2
                  style={{
                    fontSize: "1.3rem",
                    marginTop: "1rem",
                    marginBottom: "0.5rem",
                    fontWeight: "bold",
                  }}
                  {...props}
                />
              ),
              h3: ({ node, ...props }) => (
                <h3
                  style={{
                    fontSize: "1.2rem",
                    marginTop: "0.75rem",
                    marginBottom: "0.5rem",
                    fontWeight: "bold",
                  }}
                  {...props}
                />
              ),
              h4: ({ node, ...props }) => (
                <h4
                  style={{
                    fontSize: "1.1rem",
                    marginTop: "0.75rem",
                    marginBottom: "0.5rem",
                    fontWeight: "bold",
                  }}
                  {...props}
                />
              ),
              h5: ({ node, ...props }) => (
                <h5
                  style={{
                    fontSize: "1rem",
                    marginTop: "0.5rem",
                    marginBottom: "0.5rem",
                    fontWeight: "bold",
                  }}
                  {...props}
                />
              ),
              h6: ({ node, ...props }) => (
                <h6
                  style={{
                    fontSize: "0.9rem",
                    marginTop: "0.5rem",
                    marginBottom: "0.5rem",
                    fontWeight: "bold",
                  }}
                  {...props}
                />
              ),
            }}
            className="markdown-body"
            rehypePlugins={[rehypeRaw]}
          >
            {message.content}
          </ReactMarkdown>
          <div className="message-images">
            {message.data?.images &&
              message.data.images.map((image: any, index: number) => (
                <div key={index} className="message-image">
                  <img
                    src={image.url}
                    alt={`Generated image ${index + 1}`}
                    onClick={() => handleImageDownload(image.url)}
                    style={{ cursor: "pointer" }}
                  />
                  {image.prompt && (
                    <div className="image-prompt">{image.prompt}</div>
                  )}
                </div>
              ))}
          </div>
        </div>
      );
    }

    // Regular message content
    return (
      <div className="chat-message-content">
        <ReactMarkdown
          components={{
            h1: ({ node, ...props }) => (
              <h1
                style={{
                  fontSize: "1.5rem",
                  marginTop: "1rem",
                  marginBottom: "0.5rem",
                  fontWeight: "bold",
                }}
                {...props}
              />
            ),
            h2: ({ node, ...props }) => (
              <h2
                style={{
                  fontSize: "1.3rem",
                  marginTop: "1rem",
                  marginBottom: "0.5rem",
                  fontWeight: "bold",
                }}
                {...props}
              />
            ),
            h3: ({ node, ...props }) => (
              <h3
                style={{
                  fontSize: "1.2rem",
                  marginTop: "0.75rem",
                  marginBottom: "0.5rem",
                  fontWeight: "bold",
                }}
                {...props}
              />
            ),
            h4: ({ node, ...props }) => (
              <h4
                style={{
                  fontSize: "1.1rem",
                  marginTop: "0.75rem",
                  marginBottom: "0.5rem",
                  fontWeight: "bold",
                }}
                {...props}
              />
            ),
            h5: ({ node, ...props }) => (
              <h5
                style={{
                  fontSize: "1rem",
                  marginTop: "0.5rem",
                  marginBottom: "0.5rem",
                  fontWeight: "bold",
                }}
                {...props}
              />
            ),
            h6: ({ node, ...props }) => (
              <h6
                style={{
                  fontSize: "0.9rem",
                  marginTop: "0.5rem",
                  marginBottom: "0.5rem",
                  fontWeight: "bold",
                }}
                {...props}
              />
            ),
            blockquote: ({ node, ...props }) => (
              <blockquote
                style={{
                  borderLeft: "4px solid #ddd",
                  paddingLeft: "1rem",
                  margin: "0.5rem 0",
                  color: "#555",
                }}
                {...props}
              />
            ),
            hr: ({ node, ...props }) => (
              <hr
                style={{
                  margin: "1rem 0",
                  border: "none",
                  height: "1px",
                  backgroundColor: "#ddd",
                }}
                {...props}
              />
            ),
            code: ({ node, className, children, ...props }: any) => {
              const match = /language-(\w+)/.exec(className || "");
              const isInline = !match && !className;
              return isInline ? (
                <code
                  style={{
                    backgroundColor: "#f0f0f0",
                    padding: "0.1rem 0.3rem",
                    borderRadius: "3px",
                    fontSize: "0.9em",
                  }}
                  {...props}
                >
                  {children}
                </code>
              ) : (
                <code
                  style={{
                    display: "block",
                    backgroundColor: "#f5f5f5",
                    padding: "0.5rem 1rem",
                    borderRadius: "4px",
                    overflowX: "auto",
                    fontSize: "0.9em",
                  }}
                  {...props}
                >
                  {children}
                </code>
              );
            },
          }}
          className="markdown-body"
          rehypePlugins={[rehypeRaw]}
        >
          {message.content}
        </ReactMarkdown>
        {message.data?.suggested_next_questions && (
          <div className="suggested-questions mt-4">
            <div className="text-xs font-semibold mb-2 text-gray-500">
              {t("suggested_questions", "Suggested Questions")}:
            </div>
            <div className="flex flex-wrap gap-2">
              {message.data.suggested_next_questions.map((q: string) => (
                <button
                  key={q}
                  className="text-xs bg-gray-100 hover:bg-gray-200 rounded-full px-3 py-1"
                  onClick={() => handleWelcomeOptionClick(q)}
                >
                  {q}
                </button>
              ))}
            </div>
          </div>
        )}
      </div>
    );
  };

  // Add CSS for enhanced markdown components
  useEffect(() => {
    // Add custom styles for markdown-enhanced elements
    const style = document.createElement("style");
    style.textContent = `
      .markdown-body .highlight {
        border-left: 4px solid #f0b849;
        padding: 0.5rem 1rem;
        margin: 0.5rem 0;
        border-radius: 4px;
      }
      
      .markdown-body .note {
        border-left: 4px solid #1890ff;
        padding: 0.5rem 1rem;
        margin: 0.5rem 0;
        border-radius: 4px;
      }
      
      .markdown-body details {
        border: 1px solid #eaeaea;
        border-radius: 4px;
        padding: 0.5rem;
        margin: 0.5rem 0;
      }
      
      .markdown-body details summary {
        cursor: pointer;
        font-weight: bold;
        padding: 0.25rem 0;
      }
      
      .markdown-body ul.checklist {
        list-style-type: none;
        padding-left: 1rem;
      }
      
      .markdown-body ul.checklist li {
        margin-bottom: 0.25rem;
        display: flex;
        align-items: flex-start;
      }
      
      .markdown-body table {
        border-collapse: collapse;
        margin: 1rem 0;
        width: 100%;
      }
      
      .markdown-body table th,
      .markdown-body table td {
        border: 1px solid #ddd;
        padding: 0.5rem;
      }
      
      .markdown-body table th {
        font-weight: bold;
        text-align: left;
      }
      
      .fallback-indicator {
        position: relative;
        display: inline-flex;
        align-items: center;
        margin-left: 8px;
        color: var(--color-warning-text, #f5a623);
      }

      .fallback-indicator .fallback-tooltip {
        visibility: hidden;
        width: 240px;
        background-color: var(--color-background-dark, #2a2a2a);
        color: #fff;
        text-align: left;
        border-radius: 6px;
        padding: 10px;
        position: absolute;
        z-index: 10;
        bottom: 125%;
        left: 50%;
        margin-left: -120px;
        opacity: 0;
        transition: opacity 0.3s;
        box-shadow: 0 2px 10px rgba(0,0,0,0.2);
      }

      .fallback-indicator:hover .fallback-tooltip {
        visibility: visible;
        opacity: 1;
      }

      .fallback-tooltip p {
        margin: 0 0 5px 0;
        font-size: 0.9em;
      }

      .fallback-tooltip ul {
        margin: 0;
        padding-left: 18px;
        font-size: 0.85em;
      }

      .fallback-tooltip li {
        margin-bottom: 4px;
      }
      
      .agent-unavailable-notification {
        position: absolute;
        bottom: 100%;
        left: 0;
        right: 0;
        margin-bottom: 8px;
        background-color: var(--color-warning-bg, #fff3cd);
        border: 1px solid var(--color-warning-border, #ffeaa7);
        border-radius: 8px;
        padding: 12px;
        box-shadow: 0 2px 10px rgba(0,0,0,0.1);
        z-index: 10;
      }

      .notification-content {
        display: flex;
        align-items: flex-start;
        gap: 12px;
      }

      .notification-icon {
        color: var(--color-warning-text, #856404);
        margin-top: 2px;
        flex-shrink: 0;
      }

      .notification-text {
        flex: 1;
      }

      .notification-text strong {
        display: block;
        margin-bottom: 4px;
        color: var(--color-warning-text, #856404);
      }

      .notification-text p {
        margin: 0 0 4px 0;
        font-size: 0.875rem;
        color: var(--color-warning-text, #856404);
      }

      .notification-dismiss {
        background: none;
        border: none;
        color: var(--color-warning-text, #856404);
        cursor: pointer;
        font-size: 18px;
        line-height: 1;
        padding: 0;
        width: 20px;
        height: 20px;
        display: flex;
        align-items: center;
        justify-content: center;
        border-radius: 50%;
        transition: background-color 0.2s;
      }

      .notification-dismiss:hover {
        background-color: rgba(0,0,0,0.1);
      }
    `;
    document.head.appendChild(style);

    return () => {
      document.head.removeChild(style);
    };
  }, []);

  // Add methods for the simple layout
  const renderSimpleHeader = () => (
    <div className="mini-chat-header">
      <div className="mini-chat-title-row">
        <div
          className="mini-chat-dropdown"
          onClick={() => setIsRenameModalOpen(!isRenameModalOpen)} // Reuse the rename modal as dropdown
        >
          <span className="mini-chat-selected">
            {currentChat ? currentChat.name : t("new_chat")}
          </span>
          <button
            className="menu-button"
            onClick={(e) => {
              e.stopPropagation();
              setShowSidebar(true);
              setUseSimpleLayout(false);
            }}
            aria-label={t("show_menu")}
          >
            <MenuIcon />
          </button>
        </div>
      </div>
    </div>
  );

  const renderSimpleMessages = () => (
    <div className="mini-chat-messages" ref={messagesEndRef}>
      {isPageLoading || isMessagesLoading ? (
        <LoadingSpinner />
      ) : messages.length === 0 ? (
        // Welcome screen
        <div className="mini-chat-welcome">
          <div className="mini-smokey-image">
            <img src={SmokeyIcon} alt="Assistant" />
          </div>
          <h3>{randomGreeting.greeting}</h3>
          <p>{randomGreeting.subtitle}</p>

          <div className="mini-thought-cloud">
            {questionsLoading ? (
              <div className="mini-thought-bubble loading">
                Loading personalized questions...
              </div>
            ) : (
              thoughtBubbles.map((bubble, index) => (
                <button
                  key={index}
                  className="mini-thought-bubble"
                  onClick={() => handleWelcomeOptionClick(bubble.prompt)}
                  title={bubble.description}
                >
                  <span className="bubble-text">{bubble.prompt}</span>
                  {bubble.agent && (
                    <span className="bubble-agent">via {bubble.agent}</span>
                  )}
                </button>
              ))
            )}
          </div>
        </div>
      ) : (
        // Simplified message thread - messages are already sorted in loadMessages
        messages.map((message) => {
          const isUser = message.role === "human";
          const messageTime = formatMessageTime(message.timestamp);

          return (
            <div
              key={message.message_id}
              className={`mini-chat-message-row ${
                isUser ? "user-message-row" : "bot-message-row"
              }`}
            >
              {!isUser && (
                <div className="mini-message-avatar bot-avatar">
                  <div className="mini-message-avatar-placeholder">
                    {message.metadata?.agent_name
                      ? message.metadata.agent_name.charAt(0)
                      : "A"}
                  </div>
                </div>
              )}
              <div className="mini-chat-message-content">
                {!isUser && (
                  <div className="mini-message-header bot-header">
                    {message.metadata?.agent_name || "Assistant"}
                    {message.metadata?.agent_role && (
                      <span className="mini-agent-role">
                        {message.metadata.agent_role}
                      </span>
                    )}
                  </div>
                )}
                <div
                  className={`mini-chat-message ${
                    isUser ? "mini-user-message" : "mini-bot-message"
                  }`}
                >
                  <div className="mini-message-content">
                    {renderMessageContent(message)}
                  </div>
                  <span className="mini-message-timestamp">{messageTime}</span>
                </div>

                {!isUser && !message.isStreaming && (
                  <div className="mini-feedback-buttons">
                    <button
                      onClick={() => handleFeedback(message.message_id, "like")}
                      className={`mini-feedback-button ${
                        feedbackGiven[message.message_id] === "like"
                          ? "feedback-given"
                          : ""
                      }`}
                    >
                      <FaThumbsUp size={12} />
                    </button>
                    <button
                      onClick={() =>
                        handleFeedback(message.message_id, "dislike")
                      }
                      className={`mini-feedback-button ${
                        feedbackGiven[message.message_id] === "dislike"
                          ? "feedback-given"
                          : ""
                      }`}
                    >
                      <FaThumbsDown size={12} />
                    </button>
                  </div>
                )}
              </div>
              {isUser && (
                <div className="mini-message-avatar user-avatar">
                  <div className="mini-message-avatar-placeholder">U</div>
                </div>
              )}
            </div>
          );
        })
      )}
    </div>
  );

  const renderSimpleInput = () => (
    <div className="mini-message-input-container">
      <textarea
        ref={textareaRef}
        value={inputValue}
        onChange={(e) => handleInputChange(e.target.value)}
        onKeyPress={(e) => e.key === "Enter" && !e.shiftKey && handleSendMessage()}
        placeholder={t("type_message")}
        className="mini-message-input"
        disabled={isLoading}
        rows={1}
        style={{ resize: 'none', overflow: 'hidden', maxHeight: '120px' }}
      />
      <button
        className="mini-send-button"
        onClick={handleSendMessage}
        disabled={isLoading || !inputValue.trim()}
      >
        <SendIcon />
      </button>
    </div>
  );

  // Modified render method
  return (
    <div className="chat-app">
      {!useSimpleLayout && (
        <aside className={`chat-sidebar ${showSidebar ? "open" : "closed"}`}>
          {/* Existing sidebar content */}
          <div className="sidebar-header">
            {/* X button on the left - always visible now */}
            <button
              className="sidebar-close-button"
              onClick={() => setShowSidebar(false)}
              aria-label={t("close_sidebar")}
            >
              <XIcon />
            </button>

            <h2>{t("chat_history")}</h2>

            <button
              className="new-chat-button"
              onClick={handleNewChat}
              aria-label={t("new_chat")}
            >
              <PlusIcon />
            </button>
          </div>

          <div className="chat-list">
            {/* Existing chat list */}
            {chats.length > 0 ? (
              chats.map((chat: Chat) => {
                const isCurrent = currentChat?.chat_id === chat.chat_id;

                return (
                  <div
                    key={chat.chat_id}
                    className={`chat-item ${isCurrent ? "active" : ""}`}
                    onClick={() => {
                      handleChatSelect(chat);
                      setIsRenameModalOpen(false);
                    }}
                    onMouseEnter={() => setHoveredChat(chat.chat_id)}
                    onMouseLeave={() => setHoveredChat(null)}
                  >
                    <div className="chat-item-content">
                      <div className="chat-name">{chat.name}</div>
                      <div className="chat-time">
                        {dayjs(chat.created_at).fromNow()}
                      </div>
                    </div>

                    {hoveredChat === chat.chat_id && (
                      <button
                        className="chat-menu-button"
                        onClick={(e) => handleContextMenu(e, chat.chat_id)}
                        aria-label={t("menu")}
                      >
                        <FaEllipsisV />
                      </button>
                    )}
                  </div>
                );
              })
            ) : (
              <div className="empty-chats-container">
                <div className="empty-chats-content">
                  <div className="empty-chats-message">{t("no_chats_yet")}</div>
                  <div className="empty-chats-subtitle">
                    {t("start_new_conversation")}
                  </div>
                </div>
              </div>
            )}
          </div>
        </aside>
      )}

      {/* Main chat area - conditionally render based on layout type */}
      {useSimpleLayout ? (
        <div className="mini-chat-container">
          {renderSimpleHeader()}
          {renderSimpleMessages()}
          {renderSimpleInput()}
        </div>
      ) : (
        <main className={`chat-main ${showSidebar ? "with-sidebar" : ""}`}>
          {/* Regular desktop/tablet layout */}
          <div className="chat-header">
            {!showSidebar && (
              <button
                className="menu-button"
                onClick={() => setShowSidebar(true)}
                aria-label={t("show_menu")}
              >
                <MenuIcon />
              </button>
            )}

            <h1 className="chat-title">
              {currentChat ? currentChat.name : t("new_chat")}
            </h1>
          </div>

          <div className="chat-messages">
            {/* Existing messages code */}
            {isPageLoading || isMessagesLoading ? (
              <LoadingSpinner />
            ) : messages.length === 0 ? (
              // Welcome screen
              <div className="welcome-screen">
                <div className="welcome-hero">
                  <img
                    src={SmokeyIcon}
                    alt="Assistant"
                    className="assistant-avatar"
                  />
                  <h2 className="welcome-title">{randomGreeting.greeting}</h2>
                  <p className="welcome-subtitle">{randomGreeting.subtitle}</p>
                </div>

                <div className="thought-cloud">
                  {questionsLoading ? (
                    <div className="thought-bubble loading">
                      Loading personalized questions...
                    </div>
                  ) : (
                    thoughtBubbles.map((bubble, index) => (
                      <button
                        key={index}
                        className="thought-bubble"
                        onClick={() => handleWelcomeOptionClick(bubble.prompt)}
                        title={bubble.description}
                      >
                        <span className="bubble-text">{bubble.prompt}</span>
                        {bubble.agent && (
                          <span className="bubble-agent">
                            via {bubble.agent}
                          </span>
                        )}
                      </button>
                    ))
                  )}
                </div>
              </div>
            ) : (
              // Message thread with existing rendering
              messages.map((message) => {
                const isUser = message.role === "human";
                const messageTime = formatMessageTime(message.timestamp);

                return (
                  <div
                    key={message.message_id}
                    className={`message ${
                      isUser ? "user-message" : "assistant-message"
                    }`}
                  >
                    <div className="message-container">
                      {!isUser && (
                        <div className="message-avatar">
                          <div className="avatar-placeholder">
                            {message.metadata?.agent_name
                              ? message.metadata.agent_name.charAt(0)
                              : "A"}
                          </div>
                        </div>
                      )}

                      <div className="message-bubble-container">
                        {!isUser && message.metadata?.agent_name && (
                          <div className="message-header bot-header">
                            {message.metadata.agent_name}
                            {message.metadata?.agent_role && (
                              <span className="agent-role">
                                {message.metadata.agent_role}
                              </span>
                            )}
                            {message.metadata?.isFallbackResponse &&
                              message.metadata?.originalRequestedAgent && (
                                <div className="fallback-indicator">
                                  <FaInfoCircle />
                                  <div className="fallback-tooltip">
                                    <p>
                                      <strong>
                                        {
                                          message.metadata
                                            .originalRequestedAgent.name
                                        }{" "}
                                        is unavailable.
                                      </strong>
                                    </p>
                                    <p>Missing requirements:</p>
                                    <ul>
                                      {message.metadata.originalRequestedAgent.missingRequirements.map(
                                        (req: string) => (
                                          <li key={req}>{req}</li>
                                        )
                                      )}
                                    </ul>
                                  </div>
                                </div>
                              )}
                          </div>
                        )}

                        <div className="message-bubble">
                          {renderMessageContent(message)}
                          <div className="message-meta">
                            <span className="message-time">{messageTime}</span>
                          </div>
                        </div>
                      </div>

                      {isUser && (
                        <div className="message-avatar user-avatar">
                          <div className="avatar-placeholder">U</div>
                        </div>
                      )}
                    </div>

                    {/* Feedback buttons for assistant messages */}
                    {!isUser && !message.isStreaming && (
                      <div className="message-actions">
                        {extractImageUrls(message.content).length > 0 && (
                          <button
                            onClick={() => handleImageDownload(message.content)}
                            className="action-button download-button"
                            aria-label={t("download_images")}
                          >
                            <FaDownload />
                          </button>
                        )}

                        <button
                          onClick={() =>
                            handleFeedback(message.message_id, "like")
                          }
                          className={`action-button feedback-button ${
                            feedbackGiven[message.message_id] === "like"
                              ? "active"
                              : ""
                          }`}
                          aria-label={t("like")}
                        >
                          <FaThumbsUp />
                        </button>

                        <button
                          onClick={() =>
                            handleFeedback(message.message_id, "dislike")
                          }
                          className={`action-button feedback-button ${
                            feedbackGiven[message.message_id] === "dislike"
                              ? "active"
                              : ""
                          }`}
                          aria-label={t("dislike")}
                        >
                          <FaThumbsDown />
                        </button>
                      </div>
                    )}
                  </div>
                );
              })
            )}

            <div ref={messagesEndRef} />
          </div>

          {/* Message input */}
          <div className="message-input-area" style={{ position: "relative" }}>
            <div className="input-container">
              <textarea
                ref={textareaRef}
                value={inputValue}
                onChange={(e) => handleInputChange(e.target.value)}
                onKeyPress={(e) => e.key === "Enter" && !e.shiftKey && handleSendMessage()}
                placeholder={t("type_message")}
                disabled={isLoading}
                className="message-input"
                rows={1}
                style={{ resize: 'none', overflow: 'hidden', maxHeight: '120px' }}
              />

              <button
                className="send-button"
                onClick={handleSendMessage}
                disabled={isLoading || !inputValue.trim()}
                aria-label={t("send")}
              >
                <FaPaperPlane />
              </button>
            </div>

            {/* Show agent unavailable notification */}
            {showAgentNotification.show && (
              <AgentUnavailableNotification
                agentStatus={showAgentNotification.agentStatus}
                onDismiss={() =>
                  setShowAgentNotification({ show: false, agentStatus: null })
                }
              />
            )}
          </div>
        </main>
      )}

      {/* Modals */}
      <Modal
        title={currentChat ? t("rename_chat") : t("chat_history")}
        open={isRenameModalOpen}
        onClose={() => setIsRenameModalOpen(false)}
      >
        {currentChat ? (
          <>
            <input
              type="text"
              value={newChatName}
              onChange={(e) => setNewChatName(e.target.value)}
              placeholder={t("enter_chat_name")}
              className="rename-input"
            />
            <Button onClick={handleRenameChat}>{t("save")}</Button>
          </>
        ) : (
          <ChatListModal
            chats={chats}
            currentChat={currentChat}
            onSelectChat={(chat) => {
              handleChatSelect(chat);
              setIsRenameModalOpen(false);
            }}
            onNewChat={handleNewChat}
          />
        )}
      </Modal>

      {/* Context menu */}
      {contextMenu.isOpen && (
        <div
          className="context-menu"
          style={{
            top: `${contextMenu.y}px`,
            left: `${contextMenu.x}px`,
          }}
          onClick={(e) => e.stopPropagation()}
        >
          <button
            className="context-menu-item"
            onClick={() => {
              const chat = chats.find((c) => c.chat_id === contextMenu.chatId);
              if (chat) {
                setCurrentChat(chat);
                openRenameModal();
              }
              closeContextMenu();
            }}
          >
            <EditIcon /> {t("rename_chat")}
          </button>

          <button
            className="context-menu-item delete"
            onClick={() => {
              const chat = chats.find((c) => c.chat_id === contextMenu.chatId);
              if (chat) {
                setCurrentChat(chat);
                handleDeleteChat();
              }
              closeContextMenu();
            }}
          >
            <TrashIcon /> {t("delete")}
          </button>
        </div>
      )}
    </div>
  );
}
