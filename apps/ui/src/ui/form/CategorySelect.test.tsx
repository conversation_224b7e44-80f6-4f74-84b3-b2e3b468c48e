import React from 'react';
import { render, screen, fireEvent } from '@testing-library/react';
import { CategorySelect } from './CategorySelect';

describe('CategorySelect', () => {
  const mockOnChange = jest.fn();

  beforeEach(() => {
    mockOnChange.mockClear();
  });

  it('renders with predefined categories', () => {
    render(
      <CategorySelect
        label="Category"
        value=""
        onChange={mockOnChange}
      />
    );

    const input = screen.getByRole('combobox');
    expect(input).toBeInTheDocument();
    expect(input).toHaveAttribute('placeholder', 'Select or type a category...');
  });

  it('shows predefined categories when clicked', () => {
    render(
      <CategorySelect
        label="Category"
        value=""
        onChange={mockOnChange}
      />
    );

    const button = screen.getByRole('button');
    fireEvent.click(button);

    // Check for some predefined categories
    expect(screen.getByText('Flower')).toBeInTheDocument();
    expect(screen.getByText('Pre-Rolls')).toBeInTheDocument();
    expect(screen.getByText('Vapes')).toBeInTheDocument();
    expect(screen.getByText('Edibles')).toBeInTheDocument();
  });

  it('allows typing custom categories', () => {
    render(
      <CategorySelect
        label="Category"
        value=""
        onChange={mockOnChange}
      />
    );

    const input = screen.getByRole('combobox');
    fireEvent.change(input, { target: { value: 'Custom Category' } });

    const button = screen.getByRole('button');
    fireEvent.click(button);

    // Should show the custom category option
    expect(screen.getByText('Custom Category')).toBeInTheDocument();
    expect(screen.getByText('(custom)')).toBeInTheDocument();
  });

  it('filters categories based on input', () => {
    render(
      <CategorySelect
        label="Category"
        value=""
        onChange={mockOnChange}
      />
    );

    const input = screen.getByRole('combobox');
    fireEvent.change(input, { target: { value: 'Fl' } });

    const button = screen.getByRole('button');
    fireEvent.click(button);

    // Should show filtered results
    expect(screen.getByText('Flower')).toBeInTheDocument();
    // Should not show unrelated categories
    expect(screen.queryByText('Vapes')).not.toBeInTheDocument();
  });

  it('calls onChange when category is selected', () => {
    render(
      <CategorySelect
        label="Category"
        value=""
        onChange={mockOnChange}
      />
    );

    const button = screen.getByRole('button');
    fireEvent.click(button);

    const flowerOption = screen.getByText('Flower');
    fireEvent.click(flowerOption);

    expect(mockOnChange).toHaveBeenCalledWith('Flower');
  });

  it('works with pre-existing values (edit mode)', () => {
    render(
      <CategorySelect
        label="Category"
        value="Edibles"
        onChange={mockOnChange}
      />
    );

    const input = screen.getByRole('combobox');
    expect(input).toHaveValue('Edibles');
  });

  it('handles custom categories in edit mode', () => {
    render(
      <CategorySelect
        label="Category"
        value="Custom Category"
        onChange={mockOnChange}
      />
    );

    const input = screen.getByRole('combobox');
    expect(input).toHaveValue('Custom Category');

    const button = screen.getByRole('button');
    fireEvent.click(button);

    // Should show the custom category as an option
    expect(screen.getByText('Custom Category')).toBeInTheDocument();
    expect(screen.getByText('(custom)')).toBeInTheDocument();
  });
});
