import { FileStream } from "../storage/FileStream";
import { Document, DocumentParams, DocumentData } from "./Document";
import DocumentAnalysisJob from "./DocumentAnalysisJob";
import App from "../app";
import Storage from "../storage/Storage";
import { RequestError } from "../core/errors";
import { Knex } from "knex";
import { logger } from "../config/logger";
import { Readable } from "stream";
import DocumentVectorJob from "./DocumentVectorJob";

/**
 * Uploads a document and initiates analysis
 * @param location_id The location ID the document belongs to
 * @param stream The file stream with document data
 * @param user_id Optional user ID for document ownership
 * @returns The uploaded document information
 */
export const uploadDocument = async (
  location_id: number,
  stream: FileStream,
  user_id?: number
): Promise<{
  document: {
    id: number;
    name: string;
    type: string;
  };
  status: string;
}> => {
  logger.info(`Document upload requested for location ${location_id}`, {
    location_id,
    file_name: stream.metadata.fileName,
    file_type: stream.metadata.mimeType,
    file_size: stream.metadata.size,
    user_id,
  });

  if (!location_id) {
    logger.error("Missing location_id in uploadDocument");
    throw new RequestError("Missing required parameter: location_id");
  }

  if (!stream) {
    logger.error("No file stream provided in uploadDocument");
    throw new RequestError("No file stream provided");
  }

  // Check if the file size is valid
  if (!stream.metadata.size || stream.metadata.size <= 0) {
    logger.error(`Invalid file size: ${stream.metadata.size}`);
    throw new RequestError("File is empty or has invalid size");
  }

  // Check if a file with the same name already exists for this location
  logger.debug(
    `Checking for existing document with name "${stream.metadata.fileName}"`
  );
  const existingDoc = await Document.query()
    .where({
      location_id,
      name: stream.metadata.fileName,
    })
    .whereNull("deleted_at")
    .first();

  if (existingDoc) {
    logger.warn(
      `Document name collision: "${stream.metadata.fileName}" already exists for location ${location_id}`
    );
    throw new RequestError(
      `A file with name "${stream.metadata.fileName}" already exists for this location`
    );
  }

  let document: Document | null = null;

  try {
    // First store the document
    logger.info(`Creating document record for "${stream.metadata.fileName}"`);
    document = await Document.insertAndFetch({
      location_id,
      name: stream.metadata.fileName,
      type: stream.metadata.mimeType,
      size: stream.metadata.size,
      status: "pending",
      metadata: {
        size: stream.metadata.size,
        file_name: stream.metadata.fileName,
        mime_type: stream.metadata.mimeType,
        field_name: stream.metadata.fieldName,
      },
    });
    logger.debug(`Document record created with ID ${document.id}`);

    // Add user ID to stream for storage path generation
    if (user_id) {
      stream.userId = user_id.toString();
      logger.debug(`Associated user ID ${user_id} with document`);
    }

    // Store document based on storage driver
    try {
      logger.info(
        `Using ${App.main.env.storage.driver} storage for document ${document.id}`
      );
      // Simplified storage path - only use location_id without document_id nesting
      const storageKey = `documents/${location_id}/${stream.metadata.fileName}`;
      logger.debug(`Storage key: ${storageKey}`);
      const storage = new Storage(App.main.env.storage);

      // Convert stream to buffer first to avoid streaming issues
      logger.debug(`Converting stream to buffer for document ${document.id}`);
      const chunks: Buffer[] = [];
      for await (const chunk of stream.file as any) {
        chunks.push(Buffer.from(chunk));
      }
      const buffer = Buffer.concat(chunks);
      logger.debug(`Stream converted to buffer of size ${buffer.length} bytes`);

      // Create a new stream from the buffer
      const bufferStream = new Readable();
      bufferStream._read = () => {}; // _read is required but we don't need to implement it
      bufferStream.push(buffer);
      bufferStream.push(null);

      logger.info(
        `Uploading document to ${App.main.env.storage.driver} storage: ${storageKey}`
      );
      await storage.upload({
        stream: bufferStream,
        url: storageKey,
        metadata: {
          documentId: document.id.toString(),
          locationId: location_id.toString(),
          contentType: stream.metadata.mimeType,
        },
      });
      logger.info(
        `Document ${document.id} successfully uploaded to ${App.main.env.storage.driver} storage`
      );

      // Update document record with storage path
      logger.debug(
        `Updating document ${document.id} with storage path: ${storageKey}`
      );
      await Document.query()
        .where({ id: document.id })
        .update({ storage_path: storageKey });
      logger.debug(`Document storage path updated`);
    } catch (storageError: any) {
      // Log the specific storage error
      logger.error(
        `${App.main.env.storage.driver} storage upload failed for document ${document.id}: ${storageError.message}`,
        storageError
      );

      // Update document status to failed
      if (document) {
        logger.debug(
          `Updating document ${document.id} status to failed due to storage error`
        );
        await Document.query()
          .where({ id: document.id })
          .update({
            status: "failed",
            data: JSON.stringify({
              error: `Storage upload failed: ${storageError.message}`,
              error_time: new Date()
                .toISOString()
                .replace("T", " ")
                .replace(/\.\d+Z$/, ""),
            }) as any,
          });
      }

      throw new Error(`Storage upload failed: ${storageError.message}`);
    }

    // Vectorization will be triggered after analysis completes to avoid memory issues
    logger.info(
      `Vectorization will be triggered after analysis completes for document ${document.id}`
    );

    // Queue document analysis job first - vectorization will be triggered after analysis completes
    logger.info(`Queueing analysis job for document ${document.id}`);
    await DocumentAnalysisJob.from({
      document_id: document.id,
      location_id,
      file: stream,
    }).queue();
    logger.debug(`Analysis job queued for document ${document.id}`);

    return {
      document: {
        id: document.id,
        name: document.name,
        type: document.type,
      },
      status: "processing",
    };
  } catch (error: any) {
    // If we created a document but failed later, update it to failed status
    if (document) {
      try {
        logger.error(
          `Error processing document ${document.id}: ${error.message}`
        );
        logger.debug(`Updating document ${document.id} status to failed`);
        // Create a properly structured data object
        const documentData = {
          error: error instanceof Error ? error.message : "Unknown error",
          error_time: new Date()
            .toISOString()
            .replace("T", " ")
            .replace(/\.\d+Z$/, ""),
        };

        await Document.query()
          .where({ id: document.id })
          .update({
            status: "failed",
            data: JSON.stringify(documentData) as any,
          });
      } catch (updateError) {
        logger.error(
          `Failed to update document ${document.id} status to failed`,
          updateError
        );
      }
    }

    logger.error(`Document upload failed: ${error.message}`, error);
    throw new RequestError(
      `Failed to upload document: ${
        error instanceof Error ? error.message : "Unknown error"
      }`
    );
  }
};

/**
 * Retrieves a document and its analysis data
 * @param document_id The document ID to retrieve
 * @param location_id The location ID the document belongs to
 * @returns The document and its analysis data
 */
export const getDocumentWithAnalysis = async (
  document_id: number,
  location_id: number
) => {
  if (!document_id) {
    throw new RequestError("Missing required parameter: document_id");
  }

  if (!location_id) {
    throw new RequestError("Missing required parameter: location_id");
  }

  const document = await Document.first((qb: Knex.QueryBuilder) =>
    qb.where({ id: document_id, location_id })
  );

  if (!document) {
    throw new RequestError(`Document ${document_id} not found`);
  }

  // Parse document.data if it's a string
  let documentData = document.data;
  if (document.data && typeof document.data === "string") {
    try {
      documentData = JSON.parse(document.data);
    } catch (e) {
      logger.error(
        `Error parsing document.data: ${
          e instanceof Error ? e.message : "Unknown error"
        }`
      );
    }
  }

  console.log("Document data:", documentData);

  return {
    document: {
      id: document.id,
      name: document.name,
      type: document.type,
      status: document.status,
      created_at: document.created_at,
    },
    analysis: documentData?.analysis,
    vectorization: documentData?.vectorization,
    vectorization_error: documentData?.vectorization_error,
    vectorization_error_time: documentData?.vectorization_error_time,
  };
};

/**
 * Lists all documents for a location
 * @param location_id The location ID to list documents for
 * @returns Array of document objects
 */
export const listDocuments = async (location_id: number) => {
  if (!location_id) {
    throw new RequestError("Missing required parameter: location_id");
  }

  const documents = await Document.query()
    .where({ location_id })
    .whereNull("deleted_at")
    .orderBy("created_at", "desc");

  return documents.map((doc: Document) => ({
    id: doc.id,
    location_id: doc.location_id,
    name: doc.name,
    type: doc.type,
    size: doc.size,
    status: doc.status,
    storage_path: doc.storage_path,
    created_at: doc.created_at,
    updated_at: doc.updated_at,
  }));
};

/**
 * Generates a download URL for a document
 * @param document_id The document ID to download
 * @param location_id The location ID the document belongs to
 * @returns The download URL for the document
 */
export const getDocumentDownloadUrl = async (
  document_id: number,
  location_id: number
): Promise<string> => {
  if (!document_id) {
    throw new RequestError("Missing required parameter: document_id");
  }

  if (!location_id) {
    throw new RequestError("Missing required parameter: location_id");
  }

  const document = await Document.first((qb) =>
    qb.where({ id: document_id, location_id })
  );

  if (!document) {
    throw new RequestError(`Document ${document_id} not found`);
  }

  if (!document.storage_path) {
    throw new RequestError("Document storage path not found");
  }

  // Generate URL based on storage driver
  return Storage.url(document.storage_path);
};

/**
 * Deletes a document (soft delete)
 * @param document_id The document ID to delete
 * @param location_id The location ID the document belongs to
 * @returns Success message
 */
export const deleteDocument = async (
  document_id: number,
  location_id: number
): Promise<{ success: boolean; message: string }> => {
  if (!document_id) {
    throw new RequestError("Missing required parameter: document_id");
  }

  if (!location_id) {
    throw new RequestError("Missing required parameter: location_id");
  }

  // First check if document exists
  const document = await Document.first((qb) =>
    qb.where({ id: document_id, location_id })
  );

  if (!document) {
    throw new RequestError(`Document ${document_id} not found`);
  }

  // Delete from vector database if it exists
  try {
    logger.info(`Deleting document ${document_id} vectors from database`);
    const { DocumentVectorService } = await import("./DocumentVectorService");
    await DocumentVectorService.deleteDocumentVectors(document_id, location_id);
    logger.info(`Document ${document_id} vectors deleted from database`);
  } catch (vectorError: any) {
    // Log error but continue with deletion - don't block the delete operation
    logger.error(
      `Error deleting document ${document_id} vectors: ${vectorError.message}`,
      vectorError
    );
  }

  // Perform soft delete using the archive method
  await Document.archive(document_id, (qb) => qb.where({ location_id }));

  return {
    success: true,
    message: `Document ${document_id} successfully deleted`,
  };
};

/**
 * Reprocesses a document that was previously uploaded
 * @param document_id The document ID to reprocess
 * @param location_id The location ID the document belongs to
 * @returns Status of the reprocessing request
 */
export const reprocessDocument = async (
  document_id: number,
  location_id: number
): Promise<{
  document: {
    id: number;
    name: string;
    type: string;
  };
  status: string;
}> => {
  logger.info(
    `Document reprocessing requested for document ${document_id} in location ${location_id}`
  );

  if (!document_id) {
    logger.error("Missing document_id in reprocessDocument");
    throw new RequestError("Missing required parameter: document_id");
  }

  if (!location_id) {
    logger.error("Missing location_id in reprocessDocument");
    throw new RequestError("Missing required parameter: location_id");
  }

  // Fetch the document with all its details
  const document = await Document.first((qb) =>
    qb.where({ id: document_id, location_id })
  );

  if (!document) {
    logger.error(`Document ${document_id} not found for reprocessing`);
    throw new RequestError(`Document ${document_id} not found`);
  }

  try {
    // Update status to pending for reprocessing
    logger.info(
      `Updating document ${document_id} status to pending for reprocessing`
    );

    // Make sure document.data is treated as an object
    let existingData = {};
    try {
      if (document.data && typeof document.data === "string") {
        const dataStr = document.data as string;
        logger.debug(
          `Document data is a string, parsing: ${dataStr.slice(0, 100)}...`
        );
        existingData = JSON.parse(dataStr);
      } else if (document.data && typeof document.data === "object") {
        // If data is already an object, use it directly
        logger.debug(
          `Document data is an object: ${JSON.stringify(document.data).slice(
            0,
            100
          )}...`
        );
        existingData = document.data;
      }
      logger.debug(
        `Existing data after parsing: ${JSON.stringify(existingData).slice(
          0,
          100
        )}...`
      );
    } catch (e) {
      logger.error(
        `Error parsing document data: ${
          e instanceof Error ? e.message : "Unknown error"
        }`
      );
      // Continue with empty object if parsing fails
    }

    // Create data update object
    const dataToUpdate = {
      ...(existingData || {}),
      reprocessed_at: new Date().toISOString(),
      previous_status: document.status,
    } as DocumentData;

    logger.debug(
      `Update data for pending status: ${JSON.stringify(dataToUpdate)}`
    );

    await Document.query()
      .where({ id: document_id })
      .update({
        status: "pending",
        data: JSON.stringify(dataToUpdate) as any,
      });

    logger.debug(`Updated document ${document_id} status to pending`);

    // Queue a special reprocess job for analysis
    // Note: Vectorization will be automatically triggered after analysis completion
    logger.info(`Queueing reprocessing job for document ${document_id}`);
    await DocumentAnalysisJob.from({
      document_id: document.id,
      location_id,
      file: null, // No file needed - the job will handle this
      is_reprocessing: true, // Signal that this is a reprocessing job
    }).queue();
    logger.debug(
      `Analysis job queued for document ${document_id} - vectorization will follow automatically`
    );

    // No need to queue vectorization separately - the analysis job will trigger it after completion
    // This prevents memory issues from concurrent processing of the same large file

    return {
      document: {
        id: document.id,
        name: document.name,
        type: document.type,
      },
      status: "processing",
    };
  } catch (error) {
    // Update document status to failed
    logger.error(
      `Document reprocessing failed for document ${document_id}: ${
        error instanceof Error ? error.message : "Unknown error"
      }`
    );

    try {
      // Make sure document.data is treated as an object
      let existingData = {};
      try {
        if (document.data && typeof document.data === "string") {
          const dataStr = document.data as string;
          logger.debug(
            `Document data is a string, parsing: ${dataStr.slice(0, 100)}...`
          );
          existingData = JSON.parse(dataStr);
        } else if (document.data && typeof document.data === "object") {
          // If data is already an object, use it directly
          logger.debug(
            `Document data is an object: ${JSON.stringify(document.data).slice(
              0,
              100
            )}...`
          );
          existingData = document.data;
        }
        logger.debug(
          `Existing data after parsing: ${JSON.stringify(existingData).slice(
            0,
            100
          )}...`
        );
      } catch (e) {
        logger.error(
          `Error parsing document data: ${
            e instanceof Error ? e.message : "Unknown error"
          }`
        );
        // Continue with empty object if parsing fails
      }

      // Create data object properly structured
      const dataToUpdate = {
        ...(existingData || {}),
        error: `Reprocessing failed: ${
          error instanceof Error ? error.message : "Unknown error"
        }`,
        error_time: new Date()
          .toISOString()
          .replace("T", " ")
          .replace(/\.\d+Z$/, ""),
      };

      logger.debug(`Data to update: ${JSON.stringify(dataToUpdate)}`);
      logger.debug(
        `Update command: Document.query().where({ id: ${document_id} }).update({ status: "failed", data: [dataToUpdate object] })`
      );

      await Document.query()
        .where({ id: document_id })
        .update({
          status: "failed",
          data: JSON.stringify(dataToUpdate) as any,
        });
    } catch (updateError) {
      logger.error(
        `Failed to update document ${document_id} status to failed after reprocessing error`,
        updateError
      );
    }

    throw new RequestError(
      `Failed to reprocess document: ${
        error instanceof Error ? error.message : "Unknown error"
      }`
    );
  }
};
