import { Context } from "koa";
import env from "../config/env";
import { JwtAdmin } from "./AuthMiddleware";
import Admin from "./Admin";

/**
 * Check if the current user is a super user based on email
 */
export const isSuperUser = (admin: Admin | JwtAdmin | { email: string }): boolean => {
  const superUserEmails = env().superUser.emails;
  
  if (!superUserEmails || superUserEmails.length === 0) {
    return false;
  }

  const userEmail = 'email' in admin ? admin.email : '';
  return superUserEmails.includes(userEmail);
};

/**
 * Middleware to check if the current user is a super user
 */
export const superUserMiddleware = async (ctx: Context, next: () => void) => {
  const admin = ctx.state.admin;
  
  if (!admin) {
    ctx.status = 401;
    ctx.body = { error: "Authentication required" };
    return;
  }

  // For JWT admin, we need to get the full admin record to check email
  let adminRecord: Admin | null | undefined = null;
  if ('id' in admin) {
    adminRecord = await Admin.first((qb) => qb.where('id', admin.id));
  }

  const userToCheck = adminRecord || admin;
  
  if (!isSuperUser(userToCheck)) {
    ctx.status = 403;
    ctx.body = { error: "Super user access required" };
    return;
  }

  return next();
};

/**
 * Check if super user functionality is enabled (has super user emails configured)
 */
export const isSuperUserEnabled = (): boolean => {
  const superUserEmails = env().superUser.emails;
  return superUserEmails && superUserEmails.length > 0;
};
