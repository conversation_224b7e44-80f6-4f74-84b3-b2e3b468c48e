import { getStorage } from "firebase-admin/storage";
import { StorageTypeConfig } from "./Storage";
import { ImageUploadTask, StorageProvider } from "./StorageProvider";
import App from "../app";
import * as admin from "firebase-admin";
import { lookup } from "mime-types";
import { logger } from "../config/logger";

export interface FirebaseConfig extends StorageTypeConfig {
  driver: "firebase";
  credentials: admin.ServiceAccount;
  bucket?: string;
}

export class FirebaseStorageProvider implements StorageProvider {
  config: FirebaseConfig;
  private firebaseApp: admin.app.App;

  constructor(config: FirebaseConfig) {
    this.config = config;
    // Initialize Firebase Admin if not already initialized
    try {
      // Try to get an existing app first
      try {
        this.firebaseApp = admin.app();
        console.log("Using existing Firebase app for storage provider");
      } catch {
        console.log("Creating new Firebase app for storage with credentials:");
        console.log({
          projectId: this.config.credentials.projectId || "(missing)",
          clientEmail: this.config.credentials.clientEmail || "(missing)",
          privateKey: this.config.credentials.privateKey ? "✓" : "✗",
        });

        const uniqueAppName = `storage-${Date.now()}`;
        this.firebaseApp = admin.initializeApp(
          {
            credential: admin.credential.cert(this.config.credentials),
            storageBucket:
              this.config.bucket || process.env.FIREBASE_STORAGE_BUCKET,
          },
          uniqueAppName
        );
        console.log(
          `Created new Firebase app for storage with name: ${uniqueAppName}`
        );
      }
    } catch (error) {
      console.error("Error initializing Firebase for storage:", error);
      throw error;
    }
  }

  private ensureFirebaseApp(): void {
    // Check if Firebase app is still valid before using it
    if (!this.firebaseApp) {
      logger.error("Firebase app is not initialized, reinitializing...");
      const uniqueAppName = `storage-${Date.now()}`;
      this.firebaseApp = admin.initializeApp(
        {
          credential: admin.credential.cert(this.config.credentials),
          storageBucket:
            this.config.bucket || process.env.FIREBASE_STORAGE_BUCKET,
        },
        uniqueAppName
      );
      logger.info(`Reinitialized Firebase app for storage with name: ${uniqueAppName}`);
    }
  }

  path(filename: string, userId?: string): string {
    const extension = filename.includes(".") ? "" : ".png";

    // If userId is provided, store in user-specific directory
    if (userId) {
      return `images/${userId}/${filename}${extension}`;
    }

    // Fallback to original filename if no userId
    return filename + extension;
  }

  async upload(task: ImageUploadTask) {
    try {
      this.ensureFirebaseApp();

      const storage = getStorage(this.firebaseApp);
      const bucket = storage.bucket();
      const file = bucket.file(task.url);

      // Determine content type from file extension
      const contentType = lookup(task.url) || "image/jpeg";

      // Create write stream to Firebase Storage
      const writeStream = file.createWriteStream({
        metadata: {
          contentType,
          cacheControl: "public, max-age=31536000", // Cache for 1 year
          prompt: task.metadata?.prompt, // Add prompt to metadata if provided
          creator: "BakedBot.ai", // Add creator info
          ...task.metadata, // Include any other metadata
        },
        resumable: false, // Disable resumable uploads for better performance with small files
      });

      // Pipe the input stream to Firebase Storage
      return new Promise<void>((resolve, reject) => {
        task.stream
          .pipe(writeStream)
          .on("finish", () => {
            // Make the file publicly accessible
            file
              .makePublic()
              .then(() => resolve())
              .catch(reject);
          })
          .on("error", reject);
      });
    } catch (error) {
      logger.error("Error in Firebase storage upload:", error);
      throw error;
    }
  }

  async delete(filename: string): Promise<void> {
    try {
      this.ensureFirebaseApp();

      const storage = getStorage(this.firebaseApp);
      const bucket = storage.bucket();
      const file = bucket.file(filename);
      await file.delete();
    } catch (error) {
      logger.error("Error in Firebase storage delete:", error);
      throw error;
    }
  }

  async download(filename: string): Promise<Buffer> {
    try {
      this.ensureFirebaseApp();

      const storage = getStorage(this.firebaseApp);
      const bucket = storage.bucket();
      const file = bucket.file(filename);

      const [buffer] = await file.download();
      return buffer;
    } catch (error) {
      logger.error(`Error downloading file ${filename} from Firebase:`, error);
      throw new Error(`Failed to download file: ${error instanceof Error ? error.message : 'Unknown error'}`);
    }
  }

  static url(path: string) {
    const storageBucket = process.env.FIREBASE_STORAGE_BUCKET;
    if (!storageBucket) {
      console.error("FIREBASE_STORAGE_BUCKET not set, using default bucket");
    }
    return `https://storage.googleapis.com/${
      storageBucket || "marketing-auto-documents"
    }/${path}`;
  }
}
