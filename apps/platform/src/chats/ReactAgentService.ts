import { Chat<PERSON><PERSON>A<PERSON> } from "@langchain/openai";
import { AgentExecutor, createOpenAIFunctionsAgent } from "langchain/agents";
import { Calculator } from "@langchain/community/tools/calculator";
import { logger } from "../config/logger";
import {
  ChatPromptTemplate,
  MessagesPlaceholder,
} from "@langchain/core/prompts";
import {
  RetailerSearchTool,
  NearbyRetailersTool,
  RetailerProductsTool,
  RetailerDetailsTool,
  MarketAnalysisTool,
  CompetitorsTool,
  BrandSearchTool,
  ProductSearchTool,
  ProductAnalysisTool,
  RegionalMarketAnalysisByLocationTool,
  RegionalMarketAnalysisByRadiusTool,
} from "./tools/supabaseTools";
import {
  HumanMessage,
  SystemMessage,
  AIMessage,
} from "@langchain/core/messages";
import {
  SalesTrendsTool,
  SalesTrendsTimeSeriesTool,
  CustomerBehaviorTool,
  ProductPerformanceTool,
  ProductGapAnalysisTool,
} from "./tools/dataAnalysisTools";
import {
  MarginAnalysisTool,
  RevenueForecasterTool,
  CostOptimizationTool,
  FinancialDashboardTool,
} from "./tools/financialAnalysisTools";

export interface ReactAgentResponse {
  output: string;
  success: boolean;
  error?: string;
}

export class ReactAgentService {
  private model: ChatOpenAI;
  private agent: AgentExecutor | null = null;
  private tools: any[] = [];

  constructor(options?: { modelName?: string; temperature?: number }) {
    // Initialize OpenAI model
    this.model = new ChatOpenAI({
      modelName: options?.modelName || "gpt-4.1-mini",
      temperature: options?.temperature || 0.7,
      openAIApiKey: process.env.OPENAI_API_KEY,
    });

    // Initialize Agent with tools
    this.initializeAgent().catch((err) =>
      logger.error("React agent init error:", err)
    );
  }

  private async initializeAgent(): Promise<void> {
    try {
      // Initialize Supabase tools (marketplace and competitor analysis)
      const supabaseTools = [
        RetailerSearchTool,
        NearbyRetailersTool,
        RetailerProductsTool,
        RetailerDetailsTool,
        MarketAnalysisTool,
        CompetitorsTool,
        BrandSearchTool,
        ProductSearchTool,
        ProductAnalysisTool,
        RegionalMarketAnalysisByLocationTool,
        RegionalMarketAnalysisByRadiusTool,
      ];

      // Initialize data analysis tools (sales and performance analysis)
      const dataAnalysisTools = [
        new SalesTrendsTool(),
        new SalesTrendsTimeSeriesTool(),
        new CustomerBehaviorTool(),
        new ProductPerformanceTool(),
        new ProductGapAnalysisTool(),
      ];

      // Initialize financial analysis tools (margin, forecasting, cost optimization)
      const financialAnalysisTools = [
        new MarginAnalysisTool(),
        new RevenueForecasterTool(),
        new CostOptimizationTool(),
        new FinancialDashboardTool(),
      ];

      // Basic utility tools
      const utilityTools = [new Calculator()];

      // Combine all tool groups
      this.tools = [
        ...supabaseTools,
        ...dataAnalysisTools,
        ...financialAnalysisTools,
        ...utilityTools,
      ];

      // Create a prompt template for the cannabis industry
      const promptTemplate = ChatPromptTemplate.fromMessages([
        new SystemMessage(
          "You are an expert assistant for cannabis businesses. " +
            "Use the following tools to search and analyze cannabis retailer, product, and financial data. " +
            "Always think step-by-step about what information you need before using a tool. " +
            "CONTEXTUAL INFORMATION WILL BE PROVIDED, including a 'Current Location ID' and/or 'Current Retailer ID'. " +
            "IMPORTANT DISTINCTION: " +
            "- 'Current Location ID' refers to the internal business location (like a specific dispensary branch) " +
            "- 'Current Retailer ID' refers to the retailer identifier used in product and competitor databases " +
            "For product queries (like 'retailer_products'), use the 'Current Retailer ID' if provided. " +
            "For competitor analysis (like 'get_competitors'), use the 'Current Location ID' if provided. " +
            "IMPORTANT: When working with competitor data, note that 'retailer_id' and 'competitor_place_id' " +
            "refer to the same identifier. Use the exact ID values provided in queries and never make up IDs. " +
            "Always report the exact IDs returned from the database. If looking up a specific competitor, " +
            "use the 'get_competitors' tool first to get their correct retailer_id. " +
            "CRITICAL FOR 'get_competitors' TOOL: This tool REQUIRES a 'locationId' parameter. " +
            "The 'Current Location ID' will be provided in the input. You MUST use this ID. " +
            'Example call: get_competitors({"locationId": CURRENT_LOCATION_ID_VALUE}). ' +
            "DO NOT ask for the location ID if it's already provided as 'Current Location ID'. Invoke 'get_competitors' directly with the given ID. " +
            "CRITICAL FOR 'retailer_products' TOOL: This tool REQUIRES a 'retailerId' parameter. " +
            "Use the 'Current Retailer ID' if provided in the input. " +
            'Example call: retailer_products({"retailerId": "CURRENT_RETAILER_ID_VALUE"}). ' +
            "For financial analysis, first understand the user's specific financial question, then select the " +
            "appropriate financial tool such as margin analysis, revenue forecasting, cost optimization, or " +
            "financial dashboard generation. " +
            "REGIONAL MARKET ANALYSIS: You have access to powerful regional analysis tools: " +
            "- Use 'RegionalMarketAnalysisByLocationTool' for city/state-wide analysis (e.g., 'Detroit market', 'California cannabis market') " +
            "- Use 'RegionalMarketAnalysisByRadiusTool' for radius-based analysis (e.g., 'competitors within 25 miles', 'market within 50 miles') " +
            "These tools provide regional pricing trends, competitor density, market opportunities, and geographic insights. " +
            "When users ask about regional markets, geographic analysis, or market conditions in specific areas, prioritize these tools. " +
            "When competitor information is available, include the competitor names in your response."
        ),
        new MessagesPlaceholder("chat_history"),
        ["human", "{input}"],
        new MessagesPlaceholder("agent_scratchpad"),
      ]);

      // Create the agent
      const agent = await createOpenAIFunctionsAgent({
        llm: this.model,
        tools: this.tools,
        prompt: promptTemplate,
      });

      // Create the agent executor
      this.agent = new AgentExecutor({
        agent,
        tools: this.tools,
        verbose: process.env.NODE_ENV === "development",
      });

      logger.info("React agent initialized successfully with financial tools");
    } catch (error: any) {
      logger.error("Failed to initialize React agent:", error);
      throw new Error(`Agent initialization failed: ${error.message}`);
    }
  }

  /**
   * Execute the React Agent with a query
   */
  async executeQuery(
    query: string,
    chatHistory: string[] | Array<{ role: string; content: string }> = [],
    locationId?: number,
    userId?: number,
    retailerId?: string
  ): Promise<ReactAgentResponse> {
    if (!this.agent) {
      return {
        output: "Agent is not initialized yet. Please try again later.",
        success: false,
        error: "Agent not initialized",
      };
    }

    try {
      // Convert chatHistory to string format if it's an array of objects
      const stringChatHistory =
        Array.isArray(chatHistory) && chatHistory.length > 0
          ? typeof chatHistory[0] === "string"
            ? (chatHistory as string[])
            : (chatHistory as Array<{ role: string; content: string }>).map(
                (m) => `${m.role}: ${m.content}`
              )
          : [];

      // Format chat history for context
      const formattedHistory =
        stringChatHistory.length > 0
          ? "Chat history:\n" + stringChatHistory.join("\n")
          : "";

      // Format chat history for LangChain MessagePlaceholder
      const formattedLangChainHistory =
        Array.isArray(chatHistory) && chatHistory.length > 0
          ? typeof chatHistory[0] === "string"
            ? (chatHistory as string[]).map((message) => {
                // Parse "role: content" format
                const parts = message.split(": ", 2);
                const role = parts[0];
                const content = parts.length > 1 ? parts[1] : message;
                // Return HumanMessage or AIMessage directly
                return role === "user"
                  ? new HumanMessage(content)
                  : new AIMessage(content);
              })
            : (chatHistory as Array<{ role: string; content: string }>).map(
                (m) =>
                  m.role === "user"
                    ? new HumanMessage(m.content)
                    : new AIMessage(m.content)
              )
          : [];

      // Add location context if available
      const contextualQuery = query;
      let inputForAgent = query; // Default input

      // Build context information
      const contextInfo = [];
      if (locationId) {
        contextInfo.push(`Current Location ID: ${locationId}`);
      }
      if (retailerId) {
        contextInfo.push(`Current Retailer ID: ${retailerId}`);
      }

      if (contextInfo.length > 0) {
        inputForAgent = `${contextInfo.join("\n")}\n\nUser Query: ${query}`;
      }

      logger.info({
        message: "Executing React agent",
        inputForAgent, // Log the actual input being sent to the agent
        hasHistory: stringChatHistory.length > 0,
        locationId,
        retailerId,
      });

      // Execute the React Agent with the enhanced query
      const result = await this.agent.invoke({
        input: inputForAgent, // Use the more direct inputForAgent
        chat_history: formattedLangChainHistory,
      });

      logger.info({
        message: "React agent execution complete",
        outputLength: result.output?.length || 0,
      });

      return {
        output: result.output || "No output generated.",
        success: true,
      };
    } catch (error: any) {
      logger.error("Error in React agent execution:", error);
      return {
        output: `Error: ${error.message || "Unknown error occurred"}`,
        success: false,
        error: error.message || "Unknown error occurred",
      };
    }
  }

  /**
   * Reset the agent by reinitializing it
   */
  async reset(): Promise<void> {
    try {
      await this.initializeAgent();
      logger.info("React agent reset successfully");
    } catch (error) {
      logger.error("Error resetting React agent:", error);
      throw error;
    }
  }
}

// Create singleton instance
export const reactAgentService = new ReactAgentService();
