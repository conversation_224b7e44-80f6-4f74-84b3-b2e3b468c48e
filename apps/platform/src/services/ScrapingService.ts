import axios from "axios";
import { logger } from "../config/logger";
import { DefaultRedis, cacheGet, cacheSet } from "../config/redis";
import { SupabaseService } from "../supabase/SupabaseService";

interface ScrapingCacheEntry {
  retailerId: string;
  lastScraped: Date;
  productCount: number;
  success: boolean;
  errorMessage?: string;
}

interface ScrapingApiResponse {
  success: boolean;
  count?: number;
  message?: string;
  data?: any;
}

/**
 * Service for managing expensive scraping operations efficiently
 * Prevents unnecessary calls during onboarding searches and implements intelligent caching
 */
export class ScrapingService {
  private static instance: ScrapingService;
  private pendingScrapingRequests = new Map<string, Date>();
  private readonly SCRAPING_COOLDOWN_MINUTES = 30; // Don't scrape same retailer within 30 minutes
  private readonly SCRAPE_URL = "https://cannabis-marketing-chatbot-224bde0578da.herokuapp.com/api/v1/scrape-multiple-retailers?source=cannmenus";
  private readonly CACHE_TTL_HOURS = 24; // Cache scraping results for 24 hours
  private readonly CACHE_PREFIX = "scraping_cache:";
  private redis = DefaultRedis({
    host: process.env.REDIS_HOST || 'localhost',
    port: parseInt(process.env.REDIS_PORT || '6379'),
    tls: process.env.REDIS_TLS === 'true'
  });

  static getInstance(): ScrapingService {
    if (!ScrapingService.instance) {
      ScrapingService.instance = new ScrapingService();
    }
    return ScrapingService.instance;
  }

  /**
   * Get cached scraping result for a retailer
   */
  private async getCachedScrapingResult(retailerId: string): Promise<ScrapingCacheEntry | null> {
    try {
      const cacheKey = `${this.CACHE_PREFIX}${retailerId}`;
      const cached = await cacheGet<ScrapingCacheEntry>(this.redis, cacheKey);

      if (cached) {
        const hoursSinceLastScrape = (Date.now() - new Date(cached.lastScraped).getTime()) / (1000 * 60 * 60);
        if (hoursSinceLastScrape < this.CACHE_TTL_HOURS) {
          return cached;
        }
        // Cache expired, remove it
        await this.redis.del(cacheKey);
      }

      return null;
    } catch (error) {
      logger.error(`Error getting cached scraping result for ${retailerId}:`, error);
      return null;
    }
  }

  /**
   * Cache scraping result for a retailer
   */
  private async setCachedScrapingResult(
    retailerId: string,
    productCount: number,
    success: boolean,
    errorMessage?: string
  ): Promise<void> {
    try {
      const cacheKey = `${this.CACHE_PREFIX}${retailerId}`;
      const cacheEntry: ScrapingCacheEntry = {
        retailerId,
        lastScraped: new Date(),
        productCount,
        success,
        errorMessage
      };

      const ttlSeconds = this.CACHE_TTL_HOURS * 60 * 60;
      await cacheSet(this.redis, cacheKey, cacheEntry, ttlSeconds);

      logger.debug(`Cached scraping result for retailer ${retailerId}: ${productCount} products`);
    } catch (error) {
      logger.error(`Error caching scraping result for ${retailerId}:`, error);
    }
  }

  /**
   * Check if a retailer was recently scraped to avoid duplicate expensive calls
   * Now checks both in-memory cache and Redis cache
   */
  private async wasRecentlyScraped(retailerId: string): Promise<boolean> {
    // Check in-memory cache first (fastest)
    const lastScraped = this.pendingScrapingRequests.get(retailerId);
    if (lastScraped) {
      const cooldownMs = this.SCRAPING_COOLDOWN_MINUTES * 60 * 1000;
      const timeSinceLastScrape = Date.now() - lastScraped.getTime();
      if (timeSinceLastScrape < cooldownMs) {
        return true;
      }
    }

    // Check Redis cache for longer-term tracking
    const cachedResult = await this.getCachedScrapingResult(retailerId);
    if (cachedResult) {
      const hoursSinceLastScrape = (Date.now() - new Date(cachedResult.lastScraped).getTime()) / (1000 * 60 * 60);
      // If scraped within last 4 hours and was successful, consider it recent
      if (hoursSinceLastScrape < 4 && cachedResult.success && cachedResult.productCount > 0) {
        return true;
      }
    }

    return false;
  }

  /**
   * Check if retailers need scraping by examining database product counts and cache
   */
  async checkRetailersNeedingScraping(retailerIds: string[]): Promise<{
    needsScraping: string[];
    hasProducts: string[];
    recentlyScraped: string[];
  }> {
    const needsScraping: string[] = [];
    const hasProducts: string[] = [];
    const recentlyScraped: string[] = [];

    for (const retailerId of retailerIds) {
      // Check if recently scraped first
      if (await this.wasRecentlyScraped(retailerId)) {
        recentlyScraped.push(retailerId);
        continue;
      }

      // Check database for product count (this would require SupabaseService integration)
      // For now, we'll use the cache to determine if scraping is needed
      const cachedResult = await this.getCachedScrapingResult(retailerId);

      if (cachedResult) {
        if (cachedResult.productCount > 0) {
          hasProducts.push(retailerId);
        } else {
          needsScraping.push(retailerId);
        }
      } else {
        // No cache data, assume needs scraping
        needsScraping.push(retailerId);
      }
    }

    return { needsScraping, hasProducts, recentlyScraped };
  }

  /**
   * Mark a retailer as recently scraped
   */
  private markAsScraped(retailerId: string): void {
    this.pendingScrapingRequests.set(retailerId, new Date());
  }

  /**
   * Trigger scraping for retailers with explicit user consent
   * This should only be called when user explicitly requests product data
   */
  async triggerScrapingWithConsent(
    retailerIds: string[],
    states: string[],
    userInitiated: boolean = false
  ): Promise<{ success: boolean; message: string; skippedCount: number }> {
    try {
      // Use intelligent filtering based on cache and database
      const scrapingAnalysis = await this.checkRetailersNeedingScraping(retailerIds);
      const retailersToScrape = scrapingAnalysis.needsScraping;
      const skippedCount = retailerIds.length - retailersToScrape.length;

      logger.info(
        `Scraping analysis: ${retailersToScrape.length} need scraping, ` +
        `${scrapingAnalysis.hasProducts.length} have products, ` +
        `${scrapingAnalysis.recentlyScraped.length} recently scraped`
      );

      if (retailersToScrape.length === 0) {
        return {
          success: true,
          message: `All ${retailerIds.length} retailers either have products or were recently scraped. Skipping to avoid duplicate costs.`,
          skippedCount: retailerIds.length
        };
      }

      if (skippedCount > 0) {
        logger.info(
          `Skipping ${skippedCount} retailers (${scrapingAnalysis.hasProducts.length} have products, ` +
          `${scrapingAnalysis.recentlyScraped.length} recently scraped). ` +
          `Proceeding with ${retailersToScrape.length} retailers.`
        );
      }

      // Group retailers by state for efficient scraping
      const retailersByState = this.groupRetailersByState(retailersToScrape, states);

      let totalScraped = 0;
      for (const [state, stateRetailerIds] of Object.entries(retailersByState)) {
        const response = await axios.post(this.SCRAPE_URL, {
          retailer_ids: stateRetailerIds,
          states: [state],
        });

        // Parse response to get product counts if available
        const responseData: ScrapingApiResponse = response.data;
        const productCount = responseData.count || 0;

        // Cache the scraping results
        for (const retailerId of stateRetailerIds) {
          await this.setCachedScrapingResult(
            retailerId,
            productCount,
            responseData.success !== false,
            responseData.success === false ? responseData.message : undefined
          );
          this.markAsScraped(retailerId);
        }

        totalScraped += stateRetailerIds.length;

        logger.info(
          `Successfully triggered scraping for ${stateRetailerIds.length} retailers in ${state}` +
          (userInitiated ? " (user-initiated)" : "") +
          (productCount > 0 ? `, expecting ~${productCount} products` : "")
        );
      }

      return {
        success: true,
        message: `Successfully triggered scraping for ${totalScraped} retailers across ${Object.keys(retailersByState).length} states.`,
        skippedCount
      };
    } catch (error) {
      logger.error("Error triggering scraping operation:", error);
      return {
        success: false,
        message: `Failed to trigger scraping: ${error instanceof Error ? error.message : String(error)}`,
        skippedCount: 0
      };
    }
  }

  /**
   * Group retailer IDs by their states
   */
  private groupRetailersByState(retailerIds: string[], states: string[]): Record<string, string[]> {
    // For now, assume all retailers belong to the first state
    // In a more sophisticated implementation, you'd look up each retailer's state
    const primaryState = states[0];
    if (!primaryState) {
      logger.warn("No states provided for scraping operation");
      return {};
    }

    return {
      [primaryState]: retailerIds
    };
  }

  /**
   * Log retailers that need scraping without triggering expensive operations
   * This is used during search operations to identify potential scraping candidates
   * Now uses intelligent caching to provide better insights
   */
  async logRetailersNeedingScraping(
    retailerIds: string[],
    context: string = "search operation"
  ): Promise<void> {
    if (retailerIds.length === 0) return;

    const scrapingAnalysis = await this.checkRetailersNeedingScraping(retailerIds);

    if (scrapingAnalysis.needsScraping.length > 0) {
      logger.info(
        `Found ${scrapingAnalysis.needsScraping.length} retailers needing scraping during ${context}. ` +
        `Retailer IDs: ${scrapingAnalysis.needsScraping.join(', ')}. ` +
        `Consider manual scraping if product data is needed.`
      );
    }

    if (scrapingAnalysis.hasProducts.length > 0) {
      logger.debug(
        `${scrapingAnalysis.hasProducts.length} retailers already have product data (cached).`
      );
    }

    if (scrapingAnalysis.recentlyScraped.length > 0) {
      logger.debug(
        `${scrapingAnalysis.recentlyScraped.length} retailers were recently scraped and don't need immediate re-scraping.`
      );
    }
  }

  /**
   * Get retailers that definitely need scraping (no cache, no products)
   * This is useful for batch processing
   */
  async getRetailersNeedingScrapingBatch(retailerIds: string[]): Promise<string[]> {
    const analysis = await this.checkRetailersNeedingScraping(retailerIds);
    return analysis.needsScraping;
  }

  /**
   * Get statistics about scraping operations
   */
  getScrapingStats(): {
    totalTrackedRetailers: number;
    recentlyScrapedCount: number;
    oldestScrapeTime: Date | null;
    } {
    const now = new Date();
    const cooldownMs = this.SCRAPING_COOLDOWN_MINUTES * 60 * 1000;
    
    let recentlyScrapedCount = 0;
    let oldestScrapeTime: Date | null = null;

    for (const [_, scrapeTime] of this.pendingScrapingRequests) {
      const timeSinceLastScrape = now.getTime() - scrapeTime.getTime();
      
      if (timeSinceLastScrape < cooldownMs) {
        recentlyScrapedCount++;
      }

      if (!oldestScrapeTime || scrapeTime < oldestScrapeTime) {
        oldestScrapeTime = scrapeTime;
      }
    }

    return {
      totalTrackedRetailers: this.pendingScrapingRequests.size,
      recentlyScrapedCount,
      oldestScrapeTime
    };
  }

  /**
   * Clear old scraping records to prevent memory leaks
   */
  cleanupOldRecords(): void {
    const now = new Date();
    const maxAgeMs = 24 * 60 * 60 * 1000; // 24 hours
    
    let cleanedCount = 0;
    for (const [retailerId, scrapeTime] of this.pendingScrapingRequests) {
      const age = now.getTime() - scrapeTime.getTime();
      if (age > maxAgeMs) {
        this.pendingScrapingRequests.delete(retailerId);
        cleanedCount++;
      }
    }

    if (cleanedCount > 0) {
      logger.info(`Cleaned up ${cleanedCount} old scraping records`);
    }
  }
}

// Export singleton instance
export const scrapingService = ScrapingService.getInstance();
