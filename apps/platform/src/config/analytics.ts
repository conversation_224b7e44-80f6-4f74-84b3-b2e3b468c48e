// Mixpanel Analytics Configuration
import { logger } from "./logger";
import env from "./env";

// Grab the Mixpanel factory
const Mixpanel = require("mixpanel");

// Create an instance of the mixpanel client
let mixpanel: any = null;

export const initializeMixpanel = (token?: string) => {
  const mixpanelToken = token || env().mixpanel.token;

  if (!mixpanelToken) {
    logger.warn("Mixpanel token not provided, analytics will be disabled");
    return null;
  }

  try {
    mixpanel = Mixpanel.init(mixpanelToken);
    logger.info("Mixpanel initialized successfully");
    return mixpanel;
  } catch (error) {
    logger.error("Failed to initialize Mixpanel:", error);
    return null;
  }
};

export const getMixpanelClient = () => {
  return mixpanel;
};

export default {
  initializeMixpanel,
  getMixpanelClient,
};
