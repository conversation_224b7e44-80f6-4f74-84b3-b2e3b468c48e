import { Tool, <PERSON><PERSON><PERSON><PERSON><PERSON>, <PERSON>lResult, RequestContext } from "./interfaces";
import { logger } from "../config/logger";
import { z } from "zod";

// Validation schema for market parameters
const marketParamsSchema = z.object({
  data_type: z.enum([
    "competitor_prices",
    "competitor_products",
    "market_trends",
    "tax_rates",
  ]),
  location_id: z.number(),
  competitor_ids: z.array(z.string()).optional(),
  category: z.string().optional(),
  brand: z.string().optional(),
  limit: z.number().optional().default(20),
});

export class MarketTool implements Tool {
  name = "market.fetch";
  description =
    "Fetch competitive market data including competitor pricing, products, and market trends";

  parameters: ToolParameter[] = [
    {
      name: "data_type",
      description:
        "Type of market data to fetch (competitor_prices, competitor_products, market_trends, tax_rates)",
      type: "string",
      required: true,
      enum: [
        "competitor_prices",
        "competitor_products",
        "market_trends",
        "tax_rates",
      ],
    },
    {
      name: "location_id",
      description: "Location ID to analyze",
      type: "number",
      required: true,
    },
    {
      name: "competitor_ids",
      description:
        "Array of competitor IDs to include (defaults to all competitors for the location)",
      type: "array",
      required: false,
    },
    {
      name: "category",
      description: "Product category to filter by",
      type: "string",
      required: false,
    },
    {
      name: "brand",
      description: "Brand name to filter by",
      type: "string",
      required: false,
    },
    {
      name: "limit",
      description: "Maximum number of results to return",
      type: "number",
      required: false,
      default: 20,
    },
  ];

  private dataHubTool: any;
  private cache: Map<string, { data: any; timestamp: number }> = new Map();
  private cacheTtl = 60 * 60 * 1000; // 1 hour cache TTL

  constructor(dataHubTool: any) {
    this.dataHubTool = dataHubTool;
  }

  async execute(
    params: Record<string, unknown>,
    ctx?: RequestContext
  ): Promise<ToolResult> {
    try {
      // Add detailed logging
      logger.info({
        message: "MarketTool.execute called",
        params,
        context: ctx,
      });

      // Validate parameters
      const validation = marketParamsSchema.safeParse(params);
      if (!validation.success) {
        logger.error({
          message: "MarketTool validation failed",
          error: validation.error.message,
        });
        return {
          status: "error",
          error: `Invalid parameters: ${validation.error.message}`,
        };
      }

      const { data_type, location_id, competitor_ids, category, brand, limit } =
        validation.data;

      logger.info({
        message: "MarketTool params validated",
        data_type,
        location_id,
        competitor_ids: competitor_ids || "not provided",
        category: category || "not provided",
        brand: brand || "not provided",
      });

      // Create cache key
      const cacheKey = `${data_type}:${location_id}:${
        competitor_ids?.join(",") || ""
      }:${category || ""}:${brand || ""}`;

      // Check cache
      const cachedResult = this.getCachedResult(cacheKey);
      if (cachedResult) {
        logger.info(`Using cached data for ${data_type}`, { ctx });
        return {
          status: "success",
          data: cachedResult,
          metadata: {
            source: "cache",
            data_type,
            location_id,
          },
        };
      }

      // Call the appropriate data fetcher based on the data type
      let result;
      switch (data_type) {
        case "competitor_prices":
          logger.info("Calling fetchCompetitorPrices");
          result = await this.fetchCompetitorPrices(
            location_id,
            competitor_ids,
            category,
            brand,
            limit
          );
          break;
        case "competitor_products":
          logger.info("Calling fetchCompetitorProducts");
          result = await this.fetchCompetitorProducts(
            location_id,
            competitor_ids,
            category,
            brand,
            limit
          );
          break;
        case "market_trends":
          logger.info("Calling fetchMarketTrends");
          result = await this.fetchMarketTrends(location_id, category);
          break;
        case "tax_rates":
          logger.info("Calling fetchTaxRates");
          result = await this.fetchTaxRates(location_id);
          break;
        default:
          logger.error(`Unsupported data type: ${data_type}`);
          return {
            status: "error",
            error: `Unsupported data type: ${data_type}`,
          };
      }

      // Log result summary
      logger.info({
        message: `MarketTool ${data_type} result obtained`,
        resultKeys: result ? Object.keys(result) : "No result",
        resultSize: result ? JSON.stringify(result).length : 0,
      });

      // Cache the result
      this.cacheResult(cacheKey, result);

      return {
        status: "success",
        data: result,
        metadata: {
          data_type,
          location_id,
          timestamp: new Date().toISOString(),
        },
      };
    } catch (error) {
      logger.error({
        message: "Error in MarketTool execution",
        error,
        errorStack: error instanceof Error ? error.stack : undefined,
      });
      return {
        status: "error",
        error:
          error instanceof Error ? error.message : "Unknown error occurred",
      };
    }
  }

  private async fetchCompetitorPrices(
    locationId: number,
    competitorIds?: string[],
    category?: string,
    brand?: string,
    limit: number = 20
  ): Promise<any> {
    // Log method entry
    logger.info({
      message: "Starting fetchCompetitorPrices",
      locationId,
      hasCompetitorIds: competitorIds && competitorIds.length > 0,
      competitorIdsCount: competitorIds ? competitorIds.length : 0,
      category,
      brand,
    });

    try {
      // Get location competitors if no competitor_ids provided
      if (!competitorIds || competitorIds.length === 0) {
        logger.info({
          message: "No competitor IDs provided, fetching from database",
          locationId,
        });

        const competitorsResult = await this.dataHubTool.execute({
          table: "location_competitors",
          columns: ["competitor_place_id", "name"],
          filters: { location_id: locationId },
        });

        if (competitorsResult.status === "error") {
          logger.error({
            message: "Failed to fetch competitors from database",
            error: competitorsResult.error,
            locationId,
          });
          throw new Error(
            `Failed to fetch competitors: ${competitorsResult.error}`
          );
        }

        competitorIds = competitorsResult.data.map(
          (comp: any) => comp.competitor_place_id
        );

        logger.info({
          message: "Retrieved competitor IDs from database",
          locationId,
          competitorCount: competitorIds?.length,
          competitorIds,
        });
      }

      if (!competitorIds || competitorIds.length === 0) {
        logger.warn({
          message: "No competitors found for location",
          locationId,
        });
        return {
          products: [],
          competitor_info: [],
          by_category: {},
          count: 0,
          message: "No competitors found for this location",
        };
      }

      // Fetch competitor products from Supabase
      const queryParams = {
        table: "products",
        columns: [
          "product_id",
          "brand_name",
          "product_name",
          "category",
          "subcategory",
          "latest_price",
          "retailer_id",
        ],
        filters: {
          retailer_id: { in: competitorIds },
          ...(category ? { category } : {}),
          ...(brand ? { brand_name: brand } : {}),
        },
        order_by: [{ column: "latest_price", direction: "asc" }],
        limit,
      };

      logger.info({
        message: "Querying products table",
        queryParams,
        competitorIdsCount: competitorIds.length,
      });

      const result = await this.dataHubTool.execute(queryParams);

      if (result.status === "error") {
        logger.error({
          message: "Failed to fetch competitor prices",
          error: result.error,
          queryParams,
        });
        throw new Error(`Failed to fetch competitor prices: ${result.error}`);
      }

      logger.info({
        message: "Product query successful",
        productCount: result.data ? result.data.length : 0,
        firstProduct:
          result.data && result.data.length > 0 ? result.data[0] : null,
      });

      // Get competitor info to enrich the results
      const competitorInfo = new Map();
      const competitorResult = await this.dataHubTool.execute({
        table: "retailers",
        columns: ["retailer_id", "name", "address", "city", "state"],
        filters: { retailer_id: { in: competitorIds } },
      });

      if (competitorResult.status === "success") {
        for (const comp of competitorResult.data) {
          competitorInfo.set(comp.retailer_id, {
            name: comp.name,
            address: comp.address,
            city: comp.city,
            state: comp.state,
          });
        }
        logger.info({
          message: "Retrieved competitor info",
          competitorCount: competitorResult.data.length,
        });
      } else {
        logger.warn({
          message: "Failed to get competitor info",
          error: competitorResult.error,
        });
      }

      // Format the response
      const formattedResults = result.data.map((product: any) => ({
        ...product,
        competitor_info: competitorInfo.get(product.retailer_id) || {
          name: "Unknown Competitor",
        },
        price_difference: null, // This would be filled with comparison to your own prices
      }));

      // Group by product category
      const groupedResults: Record<string, any[]> = {};
      formattedResults.forEach((product: any) => {
        const cat = product.category || "Uncategorized";
        if (!groupedResults[cat]) {
          groupedResults[cat] = [];
        }
        groupedResults[cat].push(product);
      });

      const response = {
        products: formattedResults,
        competitor_info: Array.from(competitorInfo.values()),
        by_category: groupedResults,
        count: formattedResults.length,
      };

      logger.info({
        message: "Completed fetchCompetitorPrices successfully",
        productCount: formattedResults.length,
        categoryCount: Object.keys(groupedResults).length,
        categories: Object.keys(groupedResults),
      });

      return response;
    } catch (error) {
      logger.error({
        message: "Error in fetchCompetitorPrices",
        error,
        locationId,
        errorStack: error instanceof Error ? error.stack : undefined,
      });
      // Return a minimal response on error
      return {
        products: [],
        competitor_info: [],
        by_category: {},
        count: 0,
        error: error instanceof Error ? error.message : "Unknown error",
      };
    }
  }

  private async fetchCompetitorProducts(
    locationId: number,
    competitorIds?: string[],
    category?: string,
    brand?: string,
    limit: number = 20
  ): Promise<any> {
    // Log method entry with parameters
    logger.info({
      message: "Starting fetchCompetitorProducts",
      locationId,
      providedCompetitorIds: competitorIds,
      hasCompetitorIds: competitorIds && competitorIds.length > 0,
      category,
      brand,
      limit,
    });

    try {
      // Similar to fetchCompetitorPrices but with more emphasis on product details
      const result = await this.fetchCompetitorPrices(
        locationId,
        competitorIds,
        category,
        brand,
        limit
      );

      // Log the result from fetchCompetitorPrices
      logger.info({
        message: "fetchCompetitorPrices result obtained",
        hasProducts: result.products && result.products.length > 0,
        productCount: result.products ? result.products.length : 0,
        competitors: result.competitor_info ? result.competitor_info.length : 0,
      });

      // Enhance with product details
      if (result.products && result.products.length > 0) {
        // Count products by competitor
        const competitorCounts: Record<string, number> = {};
        const brandCounts: Record<string, number> = {};

        result.products.forEach((product: any) => {
          const compId = product.retailer_id;
          const brandName = product.brand_name || "Unknown";

          competitorCounts[compId] = (competitorCounts[compId] || 0) + 1;
          brandCounts[brandName] = (brandCounts[brandName] || 0) + 1;
        });

        // Add statistical analysis
        result.statistics = {
          competitorCounts,
          brandCounts,
          categoryDistribution: Object.fromEntries(
            Object.entries(result.by_category || {}).map(([cat, products]) => [
              cat,
              (products as any[]).length,
            ])
          ),
        };

        // Add unique products (those only offered by competitors)
        result.unique_products = result.products
          .filter((p: any) => p.unique_to_competitor === true)
          .slice(0, 10);

        logger.info({
          message: "Enhanced competitor product data",
          competitorCountKeys: Object.keys(competitorCounts),
          brandCountKeys: Object.keys(brandCounts),
          uniqueProductCount: result.unique_products.length,
        });
      } else {
        logger.warn({
          message: "No product data available to enhance",
          locationId,
          competitorIds,
        });
      }

      return result;
    } catch (error) {
      logger.error({
        message: "Error in fetchCompetitorProducts",
        error,
        locationId,
        errorStack: error instanceof Error ? error.stack : undefined,
      });
      // Return a minimal default response on error
      return {
        products: [],
        competitor_info: [],
        error:
          error instanceof Error ? error.message : "Unknown error occurred",
      };
    }
  }

  private async fetchMarketTrends(
    locationId: number,
    category?: string
  ): Promise<any> {
    // Return trend data about the cannabis market in the location's area
    // This would typically come from market research APIs or other data sources

    // Just returning mock data for demonstration
    return {
      market_size: {
        value: 25000000,
        growth_rate: 0.12,
        year: 2023,
      },
      trends: [
        {
          category: "Flower",
          market_share: 0.42,
          growth_rate: 0.05,
          avg_price_change: -0.02,
        },
        {
          category: "Vapes",
          market_share: 0.28,
          growth_rate: 0.15,
          avg_price_change: 0.01,
        },
        {
          category: "Edibles",
          market_share: 0.18,
          growth_rate: 0.22,
          avg_price_change: 0.03,
        },
        {
          category: "Concentrates",
          market_share: 0.12,
          growth_rate: 0.08,
          avg_price_change: -0.01,
        },
      ],
      consumer_preferences: {
        top_form_factors: ["Pre-rolls", "Gummies", "Vape Pens", "Flower"],
        emerging_segments: [
          "Beverages",
          "Fast-acting edibles",
          "Minor cannabinoids",
        ],
        declining_segments: ["High-THC only products", "Plain packaging"],
      },
      regional_insights: {
        key_demographics: ["Millennials", "Gen X", "Medical patients"],
        seasonal_factors: [
          "Summer sees 15% increase in sales",
          "Holiday season peaks in December",
        ],
      },
    };
  }

  private async fetchTaxRates(locationId: number): Promise<any> {
    // Get location details to determine state
    const locationResult = await this.dataHubTool.execute({
      table: "locations",
      columns: ["id", "state", "city", "zip"],
      filters: { id: locationId },
    });

    if (locationResult.status === "error" || !locationResult.data?.length) {
      throw new Error(
        `Failed to fetch location info: ${
          locationResult.error || "Location not found"
        }`
      );
    }

    const location = locationResult.data[0];
    const { state, city } = location;

    // Fetch tax rates for state (mock data - would connect to tax API)
    const stateTaxRates = this.getStateTaxRates(state);

    return {
      location_id: locationId,
      state,
      city,
      tax_rates: {
        state_cannabis_tax: stateTaxRates.cannabis_tax,
        state_sales_tax: stateTaxRates.sales_tax,
        local_tax: stateTaxRates.local_tax,
        excise_tax: stateTaxRates.excise_tax,
        total_tax_rate:
          stateTaxRates.cannabis_tax +
          stateTaxRates.sales_tax +
          stateTaxRates.local_tax +
          stateTaxRates.excise_tax,
      },
      tax_rules: stateTaxRates.rules,
    };
  }

  // Utility methods
  private getCachedResult(key: string): any | null {
    const cached = this.cache.get(key);

    if (cached && Date.now() - cached.timestamp < this.cacheTtl) {
      return cached.data;
    }

    return null;
  }

  private cacheResult(key: string, data: any): void {
    this.cache.set(key, {
      data,
      timestamp: Date.now(),
    });

    // Clean up old cache entries
    if (this.cache.size > 100) {
      const now = Date.now();
      for (const [k, v] of this.cache.entries()) {
        if (now - v.timestamp > this.cacheTtl) {
          this.cache.delete(k);
        }
      }
    }
  }

  private getStateTaxRates(state: string): any {
    // Mock tax rates for different states
    const taxRates: Record<string, any> = {
      CA: {
        cannabis_tax: 0.15,
        sales_tax: 0.0725,
        local_tax: 0.05,
        excise_tax: 0.15,
        rules: [
          "15% cannabis excise tax",
          "7.25% state sales tax",
          "Local taxes vary by city/county",
        ],
      },
      CO: {
        cannabis_tax: 0.15,
        sales_tax: 0.029,
        local_tax: 0.03,
        excise_tax: 0.15,
        rules: [
          "15% excise tax",
          "15% sales tax",
          "2.9% state sales tax",
          "Local taxes apply",
        ],
      },
      WA: {
        cannabis_tax: 0.37,
        sales_tax: 0.065,
        local_tax: 0.01,
        excise_tax: 0,
        rules: [
          "37% cannabis excise tax",
          "6.5% state sales tax",
          "1% local tax",
        ],
      },
      OR: {
        cannabis_tax: 0.17,
        sales_tax: 0,
        local_tax: 0.03,
        excise_tax: 0,
        rules: [
          "17% state tax on retail sales",
          "Up to 3% local tax",
          "No general sales tax",
        ],
      },
      MI: {
        cannabis_tax: 0.1,
        sales_tax: 0.06,
        local_tax: 0,
        excise_tax: 0.1,
        rules: ["10% excise tax", "6% sales tax"],
      },
    };

    return (
      taxRates[state] || {
        cannabis_tax: 0.1,
        sales_tax: 0.06,
        local_tax: 0.03,
        excise_tax: 0.1,
        rules: ["Tax rates vary by state", "Check local regulations"],
      }
    );
  }
}
