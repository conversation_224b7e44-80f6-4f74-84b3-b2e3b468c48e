import { K<PERSON> } from "knex";
import { createClient, SupabaseClient } from "@supabase/supabase-js";
import { Tool, ToolParameter, ToolResult, RequestContext } from "./interfaces";
import { logger } from "../config/logger";
import { z } from "zod";

// Define validation schemas
const columnSchema = z.string();
const filterSchema = z.record(z.string(), z.any());
const orderBySchema = z.array(
  z.object({
    column: z.string(),
    direction: z.enum(["asc", "desc"]).optional(),
  })
);

// Schema for query parameters
const queryParamsSchema = z.object({
  query_type: z.string(),
  filters: filterSchema.optional(),
  location_id: z.number().optional(),
  limit: z.number().optional(),
});

export class DataHubTool implements Tool {
  name = "data_hub.query";
  description =
    "Query the dispensary data hub for information about products, sales, customers, etc.";

  parameters: ToolParameter[] = [
    {
      name: "query_type",
      type: "string",
      description:
        "The type of query to run (e.g., 'products', 'sales', 'customers', 'market', 'competitors')",
      required: true,
    },
    {
      name: "filters",
      type: "object",
      description: "Any filters to apply to the query",
      required: false,
    },
    {
      name: "location_id",
      type: "number",
      description: "The location ID to filter data by",
      required: false,
    },
    {
      name: "limit",
      type: "number",
      description: "Limit the number of results returned",
      required: false,
    },
    {
      name: "count_only",
      type: "boolean",
      description:
        "If true, return only the count of matching records instead of full data.",
      required: false,
    },
  ];

  private db: Knex;
  private supabase: SupabaseClient;

  constructor(db: Knex, supabaseUrl: string, supabaseKey: string) {
    this.db = db;
    this.supabase = createClient(supabaseUrl, supabaseKey);
  }

  /**
   * Determines which database to use based on query type and parameters
   * @param queryType The type of query to execute
   * @param params Additional query parameters
   * @returns 'mysql' or 'supabase'
   */
  private selectDatabase(queryType: string, params: any): "mysql" | "supabase" {
    // Define query types that should use the local MySQL database
    const mysqlQueryTypes = [
      "products",
      "inventory",
      "sales",
      "transactions",
      "customers",
      "customer_list",
      "campaigns",
      "documents",
      "journeys",
      "automations",
      "staff",
      "orders",
      "loyalty",
      "promotions",
      "ecommerce",
      "marketing_metrics",
      "tax_rules",
    ];

    // Define query types that should use Supabase
    const supabaseQueryTypes = [
      "competitors",
      "competitor_products",
      "market_data",
      "industry_benchmarks",
      "regional_trends",
      "demographics",
      "compliance",
      "regulations",
    ];

    // Check if this is explicitly a competitor query
    if (
      queryType.includes("competitor") ||
      params.competitor_id ||
      params.competitor_place_id
    ) {
      return "supabase";
    }

    // If it's a products query but with competitor context, use Supabase
    if (
      queryType === "products" &&
      (params.comparison === true || params.competitor_context === true)
    ) {
      return "supabase";
    }

    // If it's explicitly asking for external/market data, use Supabase
    if (queryType.includes("market") || queryType.includes("regional")) {
      return "supabase";
    }

    // Check if the query type is in the MySQL list
    if (mysqlQueryTypes.includes(queryType)) {
      return "mysql";
    }

    // Check if the query type is in the Supabase list
    if (supabaseQueryTypes.includes(queryType)) {
      return "supabase";
    }

    // Default to MySQL for dispensary-specific data
    if (params.location_id) {
      return "mysql";
    }

    // For all other cases, default to MySQL
    return "mysql";
  }

  async execute(
    params: Record<string, unknown>,
    ctx?: RequestContext
  ): Promise<ToolResult> {
    try {
      const { query_type, filters = {}, limit = 50 } = params;

      // If a top-level count_only flag is provided, ensure it is also
      // present in the filters object so that downstream MySQL logic
      // can correctly detect and handle count-only requests.
      const topLevelCountOnly = (params as any).count_only;

      if (!query_type) {
        return {
          status: "error",
          error: "query_type is required",
        };
      }

      // Add context information to filters if available
      const enhancedFilters: Record<string, any> = {
        ...(filters as object),
        location_id: params.location_id || ctx?.locationId,
      };

      if (topLevelCountOnly !== undefined) {
        enhancedFilters.count_only = topLevelCountOnly;
      }

      // Determine which database to use
      const databaseToUse = this.selectDatabase(query_type as string, {
        ...enhancedFilters,
      });

      logger.info({
        message: `DataHubTool executing query`,
        query_type,
        filters: enhancedFilters,
        database: databaseToUse,
      });

      // Execute the query on the appropriate database
      if (databaseToUse === "mysql") {
        return await this.executeQueryMySQL(
          query_type as string,
          enhancedFilters,
          limit as number
        );
      } else {
        return await this.executeQuerySupabase(
          query_type as string,
          enhancedFilters,
          limit as number
        );
      }
    } catch (error) {
      logger.error({
        message: "Error executing DataHubTool query",
        error,
      });
      return {
        status: "error",
        error: error instanceof Error ? error.message : "Unknown error",
      };
    }
  }

  /**
   * Get exact product count for a location
   */
  async getProductCount(locationId: number): Promise<number> {
    try {
      // Simple, direct query to count products for a location
      const result = await this.db("products")
        .where({ location_id: locationId })
        .count("id as count")
        .first();

      // Explicitly convert to number to fix type error
      const count = result?.count ? Number(result.count) : 0;
      return count;
    } catch (error) {
      logger.error({
        message: "Error counting products",
        locationId,
        error,
      });
      return 0;
    }
  }

  /**
   * Execute a query against the MySQL database
   */
  private async executeQueryMySQL(
    queryType: string,
    filters: any,
    limit: number
  ): Promise<ToolResult> {
    try {
      let query;

      switch (queryType) {
        case "products":
          // For product queries, check if we need a count only
          if (filters.count_only === true) {
            // Get exact product count
            const count = await this.getProductCount(filters.location_id);
            return {
              status: "success",
              data: {
                count,
                results: [], // No detailed results when count_only is requested
                query_info: {
                  type: "product_count",
                  filters,
                  database: "mysql",
                  count,
                },
              },
            };
          }
          query = this.db("products").select("*").where(filters).limit(limit);
          break;

        case "sales":
          query = this.db("sales").select("*").where(filters).limit(limit);
          break;

        case "customers":
          query = this.db("customers").select("*").where(filters).limit(limit);
          break;

        case "campaigns":
          query = this.db("campaigns").select("*").where(filters).limit(limit);
          break;

        case "documents":
          // For documents, we might want to join with document_metadata
          query = this.db("documents")
            .select("documents.*", "document_metadata.metadata")
            .leftJoin(
              "document_metadata",
              "documents.id",
              "document_metadata.document_id"
            )
            .where(filters)
            .limit(limit);
          break;

        case "journeys":
        case "automations":
          query = this.db("automations")
            .select("*")
            .where(filters)
            .limit(limit);
          break;

        default:
          // For any other types, try to query the table with the same name
          query = this.db(queryType).select("*").where(filters).limit(limit);
      }

      const results = await query;

      return {
        status: "success",
        data: {
          results,
          query_info: {
            type: queryType,
            filters,
            database: "mysql",
            count: results.length,
          },
        },
      };
    } catch (error) {
      logger.error({
        message: "MySQL query execution error",
        query_type: queryType,
        filters,
        error,
      });

      return {
        status: "error",
        error:
          error instanceof Error
            ? `MySQL query error: ${error.message}`
            : "Unknown MySQL query error",
      };
    }
  }

  /**
   * Execute a query against the Supabase database
   */
  private async executeQuerySupabase(
    queryType: string,
    filters: any,
    limit: number
  ): Promise<ToolResult> {
    try {
      let tableName: string;
      let modifiedFilters = { ...filters };

      // List of tables that actually exist in Supabase
      const availableTables = [
        "products",
        "retailers",
        "brands",
        "product_history",
        "scraper_jobs",
      ];

      // Map query types to actual existing Supabase tables
      switch (queryType) {
        case "competitors":
          // If we're querying competitor metadata
          if (filters.location_id) {
            // This is for getting the list of competitors
            // Get competitor information from location_competitors in MySQL
            try {
              const competitors = await this.db("location_competitors")
                .where({ location_id: filters.location_id })
                .select("*");

              logger.info({
                message: "Retrieved competitors from MySQL",
                count: competitors.length,
                location_id: filters.location_id,
              });

              return {
                status: "success",
                data: {
                  results: competitors,
                  query_info: {
                    type: queryType,
                    filters,
                    database: "mysql",
                    count: competitors.length,
                  },
                },
              };
            } catch (mysqlError) {
              logger.error({
                message: "Error querying competitors from MySQL",
                error: mysqlError,
              });
              throw mysqlError;
            }
          }
          tableName = "retailers"; // Use retailers table for competitor info
          break;

        case "competitor_products":
          // For competitor products, we need to query the products table with retailer_id
          tableName = "products";

          // Handle count-only queries early
          if (filters.count_only === true) {
            // If a specific competitor is provided
            if (filters.competitor_place_id) {
              const { count, error } = await this.supabase
                .from("products")
                .select("id", { count: "exact", head: true })
                .eq("retailer_id", filters.competitor_place_id);
              if (error) throw error;
              return {
                status: "success",
                data: {
                  count: count || 0,
                  results: [],
                  query_info: {
                    type: queryType,
                    filters,
                    database: "supabase",
                    count: count || 0,
                  },
                },
              };
            }

            // Count for all competitors of a location
            if (filters.location_id) {
              try {
                const competitors = await this.db("location_competitors")
                  .where({ location_id: filters.location_id })
                  .select("competitor_place_id");

                if (competitors.length === 0) {
                  return {
                    status: "success",
                    data: {
                      count: 0,
                      results: [],
                      query_info: {
                        type: queryType,
                        filters,
                        database: "supabase",
                        count: 0,
                        message: "No competitors found for this location",
                      },
                    },
                  };
                }

                const retailerIds = competitors.map(
                  (c) => c.competitor_place_id
                );

                const { count, error } = await this.supabase
                  .from("products")
                  .select("id", { count: "exact", head: true })
                  .in("retailer_id", retailerIds);
                if (error) throw error;

                return {
                  status: "success",
                  data: {
                    count: count || 0,
                    results: [],
                    query_info: {
                      type: queryType,
                      filters,
                      database: "supabase",
                      count: count || 0,
                      competitors_queried: retailerIds,
                    },
                  },
                };
              } catch (err) {
                logger.error({
                  message: "Error counting competitor products",
                  error: err,
                });
                throw err;
              }
            }
          }

          // If we have a competitor_place_id, use it as retailer_id for filtering
          if (filters.competitor_place_id) {
            modifiedFilters = {
              ...modifiedFilters,
              retailer_id: filters.competitor_place_id,
              // Remove location_id as it's not relevant in Supabase product context
              location_id: undefined,
            };
            delete modifiedFilters.location_id;
          }

          // If we're looking for all competitor products, get all the competitors first
          if (filters.location_id && !filters.competitor_place_id) {
            try {
              const competitors = await this.db("location_competitors")
                .where({ location_id: filters.location_id })
                .select("competitor_place_id");

              if (competitors.length === 0) {
                return {
                  status: "success",
                  data: {
                    results: [],
                    query_info: {
                      type: queryType,
                      filters,
                      database: "supabase",
                      count: 0,
                      message: "No competitors found for this location",
                    },
                  },
                };
              }

              const retailerIds = competitors.map((c) => c.competitor_place_id);

              if (filters.count_only === true) {
                const { count, error } = await this.supabase
                  .from("products")
                  .select("id", { count: "exact", head: true })
                  .in("retailer_id", retailerIds);
                if (error) throw error;

                return {
                  status: "success",
                  data: {
                    count: count || 0,
                    results: [],
                    query_info: {
                      type: queryType,
                      filters,
                      database: "supabase",
                      count: count || 0,
                      competitors_queried: retailerIds,
                    },
                  },
                };
              }

              const { data, error } = await this.supabase
                .from("products")
                .select("*")
                .in("retailer_id", retailerIds)
                .limit(limit);

              if (error) throw error;

              return {
                status: "success",
                data: {
                  results: data,
                  query_info: {
                    type: queryType,
                    filters,
                    database: "supabase",
                    count: data.length,
                    competitors_queried: retailerIds,
                  },
                },
              };
            } catch (err) {
              logger.error({
                message: "Error querying competitor products",
                error: err,
              });
              throw err;
            }
          }

          if (filters.count_only === true && modifiedFilters.retailer_id) {
            const { count, error } = await this.supabase
              .from("products")
              .select("id", { count: "exact", head: true })
              .eq("retailer_id", modifiedFilters.retailer_id);
            if (error) throw error;

            return {
              status: "success",
              data: {
                count: count || 0,
                results: [],
                query_info: {
                  type: queryType,
                  filters: modifiedFilters,
                  database: "supabase",
                  count: count || 0,
                },
              },
            };
          }

          break;

        case "market_data":
        case "regional_trends":
        case "industry_benchmarks":
        case "market_trends":
          // All market-related queries use the products table with appropriate filters
          tableName = "products";

          // If there's a location_id filter but we're querying market data in Supabase,
          // remove it since Supabase products aren't filtered by our location_id
          if (modifiedFilters.location_id) {
            delete modifiedFilters.location_id;
          }

          // Add any specific market-related filters
          if (queryType === "regional_trends" && filters.state) {
            // If querying regional trends, we can filter retailers by state
            try {
              // First get retailers in the specified state
              const { data: stateRetailers, error: retailerError } =
                await this.supabase
                  .from("retailers")
                  .select("retailer_id")
                  .eq("state", filters.state);

              if (retailerError) throw retailerError;

              if (stateRetailers && stateRetailers.length > 0) {
                const stateRetailerIds = stateRetailers.map(
                  (r) => r.retailer_id
                );

                // Query products from these retailers
                const { data, error } = await this.supabase
                  .from("products")
                  .select("*")
                  .in("retailer_id", stateRetailerIds)
                  .limit(limit);

                if (error) throw error;

                return {
                  status: "success",
                  data: {
                    results: data,
                    query_info: {
                      type: queryType,
                      filters,
                      database: "supabase",
                      count: data.length,
                      region: filters.state,
                    },
                  },
                };
              }
            } catch (err) {
              logger.error({
                message: "Error querying regional market data",
                error: err,
              });
              // Continue with default query as fallback
            }
          }
          break;

        case "demographics":
          // For demographics, use retailers table which might have location demographics
          tableName = "retailers";
          break;

        case "regulations":
        case "compliance":
          // We don't have a dedicated table for regulations, so provide a graceful response
          return {
            status: "success",
            data: {
              results: [],
              query_info: {
                type: queryType,
                filters,
                database: "supabase",
                count: 0,
                message:
                  "Regulatory information must be queried through the state_regulations tool instead.",
              },
            },
          };

        case "brands":
          // Direct query to brands table
          tableName = "brands";
          break;

        default:
          // Check if the query type directly matches an available table
          if (availableTables.includes(queryType)) {
            tableName = queryType;
          } else {
            // Default to products table for unknown query types
            logger.warn({
              message:
                "Unknown Supabase query type, defaulting to products table",
              query_type: queryType,
            });
            tableName = "products";
          }
      }

      // Execute the query - make sure the table exists
      if (!availableTables.includes(tableName)) {
        logger.error({
          message: "Attempted to query non-existent Supabase table",
          tableName,
          query_type: queryType,
        });

        return {
          status: "error",
          error: `The table "${tableName}" does not exist in the Supabase database. Available tables are: ${availableTables.join(
            ", "
          )}`,
        };
      }

      // Proceed with query on valid table
      let query = this.supabase.from(tableName).select("*").limit(limit);

      // Apply filters
      Object.entries(modifiedFilters).forEach(([key, value]) => {
        if (value !== undefined) {
          query = query.eq(key, value);
        }
      });

      const { data, error } = await query;

      if (error) {
        throw error;
      }

      return {
        status: "success",
        data: {
          results: data,
          query_info: {
            type: queryType,
            filters: modifiedFilters,
            database: "supabase",
            count: data.length,
          },
        },
      };
    } catch (error) {
      logger.error({
        message: "Supabase query execution error",
        query_type: queryType,
        filters,
        error,
      });

      return {
        status: "error",
        error:
          error instanceof Error
            ? `Supabase query error: ${error.message}`
            : "Unknown Supabase query error",
      };
    }
  }
}
