/**
 * Test script for THC/CBD enhancement improvements
 * This script tests the new similar product matching and external research functionality
 */

import { ProductAIEnhancementService } from "../products/ProductAIEnhancementService";
import { SimilarProductMatchingService } from "../products/SimilarProductMatchingService";
import { CannabisDataResearchService } from "../products/CannabisDataResearchService";
import { ProductParams } from "../products/ProductParams";
import { logger } from "../config/logger";

// Test product scenarios
const testProducts: ProductParams[] = [
  {
    meta_sku: "test-001",
    product_name: "Blue Dream Flower",
    brand_name: "Premium Cannabis Co",
    category: "Flower",
    subcategory: "Indoor",
    image_url: "test.jpg",
    latest_price: 35.00,
    location_id: 1,
  },
  {
    meta_sku: "test-002", 
    product_name: "OG Kush Pre-Roll",
    brand_name: "Green Valley",
    category: "Pre-Rolls",
    image_url: "test.jpg",
    latest_price: 12.00,
    location_id: 1,
  },
  {
    meta_sku: "test-003",
    product_name: "Unknown Strain Edible",
    brand_name: "Mystery Brand",
    category: "Edibles",
    subcategory: "Gummies",
    image_url: "test.jpg",
    latest_price: 25.00,
    location_id: 1,
  },
];

async function testSimilarProductMatching() {
  console.log("\n=== Testing Similar Product Matching ===");
  
  for (const product of testProducts) {
    console.log(`\nTesting product: ${product.product_name}`);
    
    try {
      const result = await SimilarProductMatchingService.findTHCCBDFromSimilarProducts(
        {
          product_name: product.product_name,
          brand_name: product.brand_name,
          category: product.category,
          subcategory: product.subcategory,
          meta_sku: product.meta_sku,
          location_id: product.location_id,
        },
        {
          minMatches: 1, // Lower threshold for testing
          maxMatches: 5,
          includeOtherLocations: true,
        }
      );

      if (result) {
        console.log("✅ Found similar product data:");
        console.log(`   THC: ${result.percentage_thc}%`);
        console.log(`   CBD: ${result.percentage_cbd}%`);
        console.log(`   Confidence: ${result.confidence}`);
        console.log(`   Source: ${result.source_details}`);
      } else {
        console.log("❌ No similar products found");
      }
    } catch (error) {
      console.error("❌ Error in similar product matching:", error);
    }
  }
}

async function testExternalResearch() {
  console.log("\n=== Testing External Cannabis Research ===");
  
  for (const product of testProducts) {
    console.log(`\nTesting external research for: ${product.product_name}`);
    
    try {
      const result = await CannabisDataResearchService.researchTHCCBDData(
        product,
        {
          timeoutMs: 3000,
          enableWebSearch: true,
        }
      );

      if (result) {
        console.log("✅ Found external research data:");
        console.log(`   THC: ${result.percentage_thc}%`);
        console.log(`   CBD: ${result.percentage_cbd}%`);
        console.log(`   Confidence: ${result.confidence}`);
        console.log(`   Source: ${result.source_details}`);
      } else {
        console.log("❌ No external research data found");
      }
    } catch (error) {
      console.error("❌ Error in external research:", error);
    }
  }
}

async function testFullEnhancement() {
  console.log("\n=== Testing Full AI Enhancement Process ===");
  
  for (const product of testProducts) {
    console.log(`\nTesting full enhancement for: ${product.product_name}`);
    
    try {
      const enhanced = await ProductAIEnhancementService.enhanceProduct(product);
      
      console.log("✅ Enhancement completed:");
      console.log(`   Status: ${enhanced.enhancement_status}`);
      console.log(`   Enhanced fields: ${enhanced.ai_enhanced_fields?.join(", ") || "none"}`);
      
      if (enhanced.percentage_thc !== undefined) {
        console.log(`   THC: ${enhanced.percentage_thc}%`);
      }
      if (enhanced.percentage_cbd !== undefined) {
        console.log(`   CBD: ${enhanced.percentage_cbd}%`);
      }
      
      if (enhanced.data_source_tracking) {
        console.log("   Data sources:");
        Object.entries(enhanced.data_source_tracking).forEach(([field, source]) => {
          console.log(`     ${field}: ${source}`);
        });
      }
      
      if (enhanced.thc_cbd_confidence) {
        console.log(`   THC/CBD confidence: ${enhanced.thc_cbd_confidence}`);
      }
    } catch (error) {
      console.error("❌ Error in full enhancement:", error);
    }
  }
}

async function testConservativePrompting() {
  console.log("\n=== Testing Conservative AI Prompting ===");
  
  // Test with a product that should NOT get THC/CBD estimates
  const unknownProduct: ProductParams = {
    meta_sku: "test-unknown",
    product_name: "Mystery Cannabis Product",
    brand_name: "Unknown Brand",
    category: "Unknown",
    image_url: "test.jpg",
    latest_price: 30.00,
    location_id: 1,
  };
  
  console.log(`Testing conservative prompting for: ${unknownProduct.product_name}`);
  
  try {
    const enhanced = await ProductAIEnhancementService.enhanceProduct(unknownProduct);
    
    console.log("✅ Conservative enhancement completed:");
    console.log(`   Status: ${enhanced.enhancement_status}`);
    console.log(`   THC: ${enhanced.percentage_thc || "null (good!)"}`);
    console.log(`   CBD: ${enhanced.percentage_cbd || "null (good!)"}`);
    
    if (enhanced.percentage_thc === null && enhanced.percentage_cbd === null) {
      console.log("✅ PASS: AI correctly avoided guessing THC/CBD percentages");
    } else {
      console.log("❌ FAIL: AI should not have estimated THC/CBD for unknown product");
    }
  } catch (error) {
    console.error("❌ Error in conservative prompting test:", error);
  }
}

async function runAllTests() {
  console.log("🧪 Starting THC/CBD Enhancement Tests");
  console.log("=====================================");
  
  try {
    await testSimilarProductMatching();
    await testExternalResearch();
    await testFullEnhancement();
    await testConservativePrompting();
    
    console.log("\n✅ All tests completed!");
    console.log("\nNext steps:");
    console.log("1. Review the test results above");
    console.log("2. Check that THC/CBD data sources are properly tracked");
    console.log("3. Verify that conservative prompting prevents random guessing");
    console.log("4. Test with real product data in your database");
  } catch (error) {
    console.error("❌ Test suite failed:", error);
  }
}

// Export for use in other test files
export {
  testSimilarProductMatching,
  testExternalResearch,
  testFullEnhancement,
  testConservativePrompting,
  runAllTests,
};

// Run tests if this file is executed directly
if (require.main === module) {
  runAllTests().catch(console.error);
}
