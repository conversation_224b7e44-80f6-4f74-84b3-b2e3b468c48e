import { logger } from "../config/logger";
import App from "../app";

export interface SimilarProductMatch {
  product_id: number;
  meta_sku: string;
  product_name: string;
  brand_name?: string;
  category?: string;
  subcategory?: string;
  percentage_thc?: number;
  percentage_cbd?: number;
  match_score: number;
  match_reasons: string[];
}

export interface THCCBDData {
  percentage_thc?: number;
  percentage_cbd?: number;
  source: 'similar_products' | 'external_api' | 'ai_estimation';
  confidence: 'high' | 'medium' | 'low';
  source_details?: string;
  similar_products_count?: number;
}

export class SimilarProductMatchingService {
  private static get db() {
    return App.main.db;
  }

  /**
   * Find similar products and extract THC/CBD data
   */
  static async findTHCCBDFromSimilarProducts(
    product: {
      product_name?: string;
      brand_name?: string;
      category?: string;
      subcategory?: string;
      meta_sku?: string;
      location_id: number;
    },
    options: {
      minMatches?: number;
      maxMatches?: number;
      includeOtherLocations?: boolean;
    } = {}
  ): Promise<THCCBDData | null> {
    const { minMatches = 2, maxMatches = 10, includeOtherLocations = true } = options;

    try {
      logger.info("Searching for similar products for THC/CBD data", {
        product_name: product.product_name,
        brand_name: product.brand_name,
        category: product.category,
        meta_sku: product.meta_sku,
        location_id: product.location_id,
      });

      // Find similar products using multiple matching strategies
      const similarProducts = await this.findSimilarProducts(product, {
        maxMatches,
        includeOtherLocations,
      });

      if (similarProducts.length < minMatches) {
        logger.info("Insufficient similar products found for reliable THC/CBD data", {
          found: similarProducts.length,
          required: minMatches,
        });
        return null;
      }

      // Extract THC/CBD data from similar products
      const thcCbdData = this.extractTHCCBDFromMatches(similarProducts);

      if (!thcCbdData.percentage_thc && !thcCbdData.percentage_cbd) {
        logger.info("No THC/CBD data found in similar products");
        return null;
      }

      logger.info("Successfully extracted THC/CBD data from similar products", {
        thc: thcCbdData.percentage_thc,
        cbd: thcCbdData.percentage_cbd,
        confidence: thcCbdData.confidence,
        similar_products_count: similarProducts.length,
      });

      return {
        ...thcCbdData,
        source: 'similar_products',
        similar_products_count: similarProducts.length,
        source_details: `Based on ${similarProducts.length} similar products`,
      };
    } catch (error) {
      logger.error("Error finding THC/CBD data from similar products:", error);
      return null;
    }
  }

  /**
   * Find similar products using multiple matching strategies
   */
  private static async findSimilarProducts(
    product: {
      product_name?: string;
      brand_name?: string;
      category?: string;
      subcategory?: string;
      meta_sku?: string;
      location_id: number;
    },
    options: {
      maxMatches: number;
      includeOtherLocations: boolean;
    }
  ): Promise<SimilarProductMatch[]> {
    const matches: SimilarProductMatch[] = [];

    // Strategy 1: Exact strain name match (highest priority)
    if (product.product_name) {
      const strainMatches = await this.findByStrainName(product, options);
      matches.push(...strainMatches);
    }

    // Strategy 2: Brand + category match
    if (product.brand_name && product.category && matches.length < options.maxMatches) {
      const brandCategoryMatches = await this.findByBrandAndCategory(product, options);
      matches.push(...brandCategoryMatches);
    }

    // Strategy 3: Category + subcategory match
    if (product.category && matches.length < options.maxMatches) {
      const categoryMatches = await this.findByCategoryAndSubcategory(product, options);
      matches.push(...categoryMatches);
    }

    // Remove duplicates and sort by match score
    const uniqueMatches = this.deduplicateMatches(matches);
    return uniqueMatches
      .sort((a, b) => b.match_score - a.match_score)
      .slice(0, options.maxMatches);
  }

  /**
   * Find products with similar strain names
   */
  private static async findByStrainName(
    product: {
      product_name?: string;
      brand_name?: string;
      category?: string;
      meta_sku?: string;
      location_id: number;
    },
    options: { includeOtherLocations: boolean }
  ): Promise<SimilarProductMatch[]> {
    if (!product.product_name) return [];

    // Extract potential strain name from product name
    const strainKeywords = this.extractStrainKeywords(product.product_name);
    if (strainKeywords.length === 0) return [];

    let query = this.db("products")
      .select(
        "id as product_id",
        "meta_sku",
        "product_name",
        "brand_name",
        "category",
        "subcategory",
        "percentage_thc",
        "percentage_cbd"
      )
      .where((builder) => {
        builder.whereNotNull("percentage_thc").orWhereNotNull("percentage_cbd");
      });

    // Location filtering
    if (options.includeOtherLocations) {
      query = query.whereNot("meta_sku", product.meta_sku || "");
    } else {
      query = query
        .where("location_id", product.location_id)
        .whereNot("meta_sku", product.meta_sku || "");
    }

    // Build strain name matching conditions
    query = query.where((builder) => {
      strainKeywords.forEach((keyword, index) => {
        if (index === 0) {
          builder.where("product_name", "like", `%${keyword}%`);
        } else {
          builder.orWhere("product_name", "like", `%${keyword}%`);
        }
      });
    });

    const results = await query.limit(20);

    return results.map((row) => ({
      ...row,
      match_score: this.calculateStrainMatchScore(product.product_name!, row.product_name, strainKeywords),
      match_reasons: ["strain_name_similarity"],
    }));
  }

  /**
   * Find products by brand and category
   */
  private static async findByBrandAndCategory(
    product: {
      brand_name?: string;
      category?: string;
      subcategory?: string;
      meta_sku?: string;
      location_id: number;
    },
    options: { includeOtherLocations: boolean }
  ): Promise<SimilarProductMatch[]> {
    if (!product.brand_name || !product.category) return [];

    let query = this.db("products")
      .select(
        "id as product_id",
        "meta_sku",
        "product_name",
        "brand_name",
        "category",
        "subcategory",
        "percentage_thc",
        "percentage_cbd"
      )
      .where("brand_name", "ilike", product.brand_name)
      .where("category", "ilike", product.category)
      .where((builder) => {
        builder.whereNotNull("percentage_thc").orWhereNotNull("percentage_cbd");
      });

    // Location filtering
    if (options.includeOtherLocations) {
      query = query.whereNot("meta_sku", product.meta_sku || "");
    } else {
      query = query
        .where("location_id", product.location_id)
        .whereNot("meta_sku", product.meta_sku || "");
    }

    // Prefer same subcategory if available
    if (product.subcategory) {
      query = query.orderByRaw(
        "CASE WHEN subcategory ILIKE ? THEN 0 ELSE 1 END",
        [product.subcategory]
      );
    }

    const results = await query.limit(15);

    return results.map((row) => ({
      ...row,
      match_score: this.calculateBrandCategoryMatchScore(product, row),
      match_reasons: ["brand_category_match"],
    }));
  }

  /**
   * Find products by category and subcategory
   */
  private static async findByCategoryAndSubcategory(
    product: {
      category?: string;
      subcategory?: string;
      meta_sku?: string;
      location_id: number;
    },
    options: { includeOtherLocations: boolean }
  ): Promise<SimilarProductMatch[]> {
    if (!product.category) return [];

    let query = this.db("products")
      .select(
        "id as product_id",
        "meta_sku",
        "product_name",
        "brand_name",
        "category",
        "subcategory",
        "percentage_thc",
        "percentage_cbd"
      )
      .where("category", "ilike", product.category)
      .where((builder) => {
        builder.whereNotNull("percentage_thc").orWhereNotNull("percentage_cbd");
      });

    // Add subcategory filter if available
    if (product.subcategory) {
      query = query.where("subcategory", "ilike", product.subcategory);
    }

    // Location filtering
    if (options.includeOtherLocations) {
      query = query.whereNot("meta_sku", product.meta_sku || "");
    } else {
      query = query
        .where("location_id", product.location_id)
        .whereNot("meta_sku", product.meta_sku || "");
    }

    const results = await query.limit(10);

    return results.map((row) => ({
      ...row,
      match_score: this.calculateCategoryMatchScore(product, row),
      match_reasons: ["category_subcategory_match"],
    }));
  }

  /**
   * Extract THC/CBD data from similar product matches
   */
  private static extractTHCCBDFromMatches(matches: SimilarProductMatch[]): {
    percentage_thc?: number;
    percentage_cbd?: number;
    confidence: 'high' | 'medium' | 'low';
  } {
    const thcValues = matches
      .filter(m => m.percentage_thc !== null && m.percentage_thc !== undefined)
      .map(m => m.percentage_thc!);

    const cbdValues = matches
      .filter(m => m.percentage_cbd !== null && m.percentage_cbd !== undefined)
      .map(m => m.percentage_cbd!);

    let percentage_thc: number | undefined;
    let percentage_cbd: number | undefined;
    let confidence: 'high' | 'medium' | 'low' = 'low';

    // Calculate THC average if we have values
    if (thcValues.length > 0) {
      percentage_thc = Math.round((thcValues.reduce((sum, val) => sum + val, 0) / thcValues.length) * 100) / 100;
    }

    // Calculate CBD average if we have values
    if (cbdValues.length > 0) {
      percentage_cbd = Math.round((cbdValues.reduce((sum, val) => sum + val, 0) / cbdValues.length) * 100) / 100;
    }

    // Determine confidence based on number of matches and consistency
    if (matches.length >= 5) {
      const thcConsistency = this.calculateValueConsistency(thcValues);
      const cbdConsistency = this.calculateValueConsistency(cbdValues);

      if (thcConsistency > 0.8 || cbdConsistency > 0.8) {
        confidence = 'high';
      } else if (thcConsistency > 0.6 || cbdConsistency > 0.6) {
        confidence = 'medium';
      }
    } else if (matches.length >= 3) {
      confidence = 'medium';
    }

    return { percentage_thc, percentage_cbd, confidence };
  }

  /**
   * Calculate consistency of values (lower variance = higher consistency)
   */
  private static calculateValueConsistency(values: number[]): number {
    if (values.length < 2) return 1;

    const mean = values.reduce((sum, val) => sum + val, 0) / values.length;
    const variance = values.reduce((sum, val) => sum + Math.pow(val - mean, 2), 0) / values.length;
    const standardDeviation = Math.sqrt(variance);

    // Convert to consistency score (0-1, where 1 is most consistent)
    const coefficientOfVariation = standardDeviation / mean;
    return Math.max(0, 1 - coefficientOfVariation);
  }

  /**
   * Extract strain keywords from product name
   */
  private static extractStrainKeywords(productName: string): string[] {
    // Remove common cannabis product words and extract potential strain names
    const commonWords = [
      'cannabis', 'marijuana', 'weed', 'flower', 'bud', 'strain', 'indica', 'sativa', 'hybrid',
      'pre-roll', 'preroll', 'joint', 'blunt', 'eighth', '1/8', 'quarter', '1/4', 'half', '1/2',
      'ounce', 'oz', 'gram', 'g', 'mg', 'thc', 'cbd', '%', 'percent'
    ];

    const words = productName
      .toLowerCase()
      .replace(/[^\w\s]/g, ' ')
      .split(/\s+/)
      .filter(word => word.length > 2 && !commonWords.includes(word));

    // Look for potential strain names (usually 2-3 words)
    const strainKeywords: string[] = [];

    // Single word strains
    words.forEach(word => {
      if (word.length > 3) {
        strainKeywords.push(word);
      }
    });

    // Two word combinations
    for (let i = 0; i < words.length - 1; i++) {
      const twoWordStrain = `${words[i]} ${words[i + 1]}`;
      strainKeywords.push(twoWordStrain);
    }

    return strainKeywords.slice(0, 5); // Limit to top 5 keywords
  }

  /**
   * Calculate match score for strain name similarity
   */
  private static calculateStrainMatchScore(
    originalName: string,
    matchName: string,
    keywords: string[]
  ): number {
    let score = 0;
    const originalLower = originalName.toLowerCase();
    const matchLower = matchName.toLowerCase();

    // Exact match bonus
    if (originalLower === matchLower) {
      return 1.0;
    }

    // Keyword matching
    keywords.forEach(keyword => {
      if (matchLower.includes(keyword.toLowerCase())) {
        score += 0.3;
      }
    });

    // Length similarity bonus
    const lengthDiff = Math.abs(originalName.length - matchName.length);
    const lengthSimilarity = 1 - (lengthDiff / Math.max(originalName.length, matchName.length));
    score += lengthSimilarity * 0.2;

    return Math.min(score, 1.0);
  }

  /**
   * Calculate match score for brand + category matches
   */
  private static calculateBrandCategoryMatchScore(
    product: { brand_name?: string; category?: string; subcategory?: string },
    match: { brand_name?: string; category?: string; subcategory?: string }
  ): number {
    let score = 0.5; // Base score for brand + category match

    // Subcategory bonus
    if (product.subcategory && match.subcategory &&
        product.subcategory.toLowerCase() === match.subcategory.toLowerCase()) {
      score += 0.3;
    }

    // Exact brand match bonus
    if (product.brand_name && match.brand_name &&
        product.brand_name.toLowerCase() === match.brand_name.toLowerCase()) {
      score += 0.2;
    }

    return Math.min(score, 1.0);
  }

  /**
   * Calculate match score for category matches
   */
  private static calculateCategoryMatchScore(
    product: { category?: string; subcategory?: string },
    match: { category?: string; subcategory?: string }
  ): number {
    let score = 0.3; // Base score for category match

    // Subcategory bonus
    if (product.subcategory && match.subcategory &&
        product.subcategory.toLowerCase() === match.subcategory.toLowerCase()) {
      score += 0.4;
    }

    return Math.min(score, 1.0);
  }

  /**
   * Remove duplicate matches based on meta_sku
   */
  private static deduplicateMatches(matches: SimilarProductMatch[]): SimilarProductMatch[] {
    const seen = new Set<string>();
    return matches.filter(match => {
      if (seen.has(match.meta_sku)) {
        return false;
      }
      seen.add(match.meta_sku);
      return true;
    });
  }
}
