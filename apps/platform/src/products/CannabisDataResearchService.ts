import { logger } from "../config/logger";
import { THCCBDData } from "./SimilarProductMatchingService";

export interface CannabisStrainInfo {
  name: string;
  thc_percentage?: number;
  cbd_percentage?: number;
  strain_type?: 'indica' | 'sativa' | 'hybrid';
  effects?: string[];
  source: string;
}

export class CannabisDataResearchService {
  private static readonly TIMEOUT_MS = 5000; // 5 second timeout for quick searches
  private static readonly MAX_RETRIES = 2;

  /**
   * Research THC/CBD data from external sources with timeout controls
   */
  static async researchTHCCBDData(
    product: {
      product_name?: string;
      brand_name?: string;
      category?: string;
    },
    options: {
      timeoutMs?: number;
      enableWebSearch?: boolean;
    } = {}
  ): Promise<THCCBDData | null> {
    const { timeoutMs = this.TIMEOUT_MS, enableWebSearch = true } = options;

    if (!enableWebSearch) {
      logger.info("Web search disabled, skipping external cannabis data research");
      return null;
    }

    try {
      logger.info("Starting external cannabis data research", {
        product_name: product.product_name,
        brand_name: product.brand_name,
        category: product.category,
        timeout: timeoutMs,
      });

      // Create a timeout promise
      const timeoutPromise = new Promise<never>((resolve, reject) => {
        setTimeout(() => reject(new Error("Research timeout")), timeoutMs);
      });

      // Race between research and timeout
      const result = await Promise.race([
        this.performCannabisResearch(product),
        timeoutPromise,
      ]);

      if (result) {
        logger.info("Successfully found external cannabis data", {
          thc: result.percentage_thc,
          cbd: result.percentage_cbd,
          confidence: result.confidence,
          source: result.source_details,
        });
        return result;
      }

      return null;
    } catch (error) {
      if (error instanceof Error && error.message === "Research timeout") {
        logger.warn("Cannabis data research timed out", {
          product_name: product.product_name,
          timeout: timeoutMs,
        });
      } else {
        logger.error("Error during cannabis data research:", error);
      }
      return null;
    }
  }

  /**
   * Perform the actual cannabis research using multiple strategies
   */
  private static async performCannabisResearch(product: {
    product_name?: string;
    brand_name?: string;
    category?: string;
  }): Promise<THCCBDData | null> {
    const strainName = this.extractStrainName(product.product_name || "");
    
    if (!strainName) {
      logger.info("No identifiable strain name found for research");
      return null;
    }

    // Try multiple research strategies in order of reliability
    const strategies = [
      () => this.searchLeaflyAPI(strainName),
      () => this.searchWeedmapsAPI(strainName),
      () => this.searchAllBudAPI(strainName),
      () => this.performWebScraping(strainName, product.brand_name),
    ];

    for (const strategy of strategies) {
      try {
        const result = await strategy();
        if (result) {
          return result;
        }
      } catch (error) {
        logger.debug("Research strategy failed, trying next:", error);
        continue;
      }
    }

    logger.info("No external cannabis data found after trying all strategies");
    return null;
  }

  /**
   * Extract strain name from product name
   */
  private static extractStrainName(productName: string): string | null {
    if (!productName) return null;

    // Remove common product descriptors and extract potential strain name
    const cleanName = productName
      .replace(/\b(pre-roll|preroll|flower|bud|eighth|1\/8|quarter|1\/4|gram|g|mg|%)\b/gi, '')
      .replace(/\b(indica|sativa|hybrid)\b/gi, '')
      .replace(/\b(thc|cbd)\b/gi, '')
      .replace(/\d+(\.\d+)?%?/g, '') // Remove percentages
      .replace(/\s+/g, ' ')
      .trim();

    // If the cleaned name is too short or generic, return null
    if (cleanName.length < 3 || /^(cannabis|marijuana|weed)$/i.test(cleanName)) {
      return null;
    }

    return cleanName;
  }

  /**
   * Search Leafly API for strain information
   * Note: This is a placeholder - actual implementation would require Leafly API key
   */
  private static async searchLeaflyAPI(strainName: string): Promise<THCCBDData | null> {
    // Placeholder for Leafly API integration
    // In a real implementation, you would:
    // 1. Make API call to Leafly's strain database
    // 2. Parse the response for THC/CBD percentages
    // 3. Return formatted data
    
    logger.debug("Leafly API search not implemented (requires API key)", { strainName });
    return null;
  }

  /**
   * Search Weedmaps API for strain information
   * Note: This is a placeholder - actual implementation would require Weedmaps API access
   */
  private static async searchWeedmapsAPI(strainName: string): Promise<THCCBDData | null> {
    // Placeholder for Weedmaps API integration
    logger.debug("Weedmaps API search not implemented (requires API access)", { strainName });
    return null;
  }

  /**
   * Search AllBud API for strain information
   * Note: This is a placeholder - actual implementation would require AllBud API key
   */
  private static async searchAllBudAPI(strainName: string): Promise<THCCBDData | null> {
    // Placeholder for AllBud API integration
    logger.debug("AllBud API search not implemented (requires API key)", { strainName });
    return null;
  }

  /**
   * Perform web scraping for strain information
   * Note: This is a simplified placeholder - actual implementation would need proper scraping
   */
  private static async performWebScraping(
    strainName: string,
    brandName?: string
  ): Promise<THCCBDData | null> {
    try {
      // This is a placeholder for web scraping implementation
      // In a real implementation, you would:
      // 1. Use a web scraping library like Puppeteer or Cheerio
      // 2. Search cannabis databases like Leafly, AllBud, etc.
      // 3. Extract THC/CBD percentages from the scraped data
      // 4. Handle rate limiting and respect robots.txt
      
      logger.debug("Web scraping not implemented (requires scraping infrastructure)", {
        strainName,
        brandName,
      });

      // For now, return null to indicate no data found
      return null;
    } catch (error) {
      logger.error("Web scraping error:", error);
      return null;
    }
  }

  /**
   * Validate and normalize THC/CBD percentages
   */
  private static validateAndNormalize(
    thc?: number,
    cbd?: number
  ): { percentage_thc?: number; percentage_cbd?: number } | null {
    const result: { percentage_thc?: number; percentage_cbd?: number } = {};

    // Validate THC percentage
    if (thc !== undefined && thc !== null) {
      if (thc >= 0 && thc <= 100) {
        result.percentage_thc = Math.round(thc * 100) / 100; // Round to 2 decimal places
      } else {
        logger.warn("Invalid THC percentage found:", thc);
      }
    }

    // Validate CBD percentage
    if (cbd !== undefined && cbd !== null) {
      if (cbd >= 0 && cbd <= 100) {
        result.percentage_cbd = Math.round(cbd * 100) / 100; // Round to 2 decimal places
      } else {
        logger.warn("Invalid CBD percentage found:", cbd);
      }
    }

    // Return null if no valid data found
    if (!result.percentage_thc && !result.percentage_cbd) {
      return null;
    }

    return result;
  }

  /**
   * Determine confidence level based on source and data quality
   */
  private static determineConfidence(
    source: string,
    hasMultipleSources: boolean = false
  ): 'high' | 'medium' | 'low' {
    // API sources are generally more reliable than web scraping
    if (source.includes('leafly') || source.includes('weedmaps')) {
      return hasMultipleSources ? 'high' : 'medium';
    }
    
    // Web scraping is less reliable
    if (source.includes('scraping') || source.includes('web')) {
      return 'low';
    }

    return 'medium';
  }

  /**
   * Create a formatted THCCBDData response
   */
  private static createResponse(
    data: { percentage_thc?: number; percentage_cbd?: number },
    source: string,
    confidence: 'high' | 'medium' | 'low'
  ): THCCBDData {
    return {
      percentage_thc: data.percentage_thc,
      percentage_cbd: data.percentage_cbd,
      source: 'external_api',
      confidence,
      source_details: `External research: ${source}`,
    };
  }

  /**
   * Check if external research is enabled and available
   */
  static isExternalResearchAvailable(): boolean {
    // Check if any external APIs are configured
    // This would check for API keys, rate limits, etc.
    return false; // Disabled by default until APIs are configured
  }
}
